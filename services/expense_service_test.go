package services

import (
	"errors"
	"go-expense/models"
	"go-expense/models/mocks"
	"testing"

	"github.com/stretchr/testify/assert"
)

// Test fixtures and helpers

func createValidExpense() *models.Expense {
	return &models.Expense{
		Amount:           25.50,
		Category:         "Food",
		Subcategory:      "Lunch",
		Description:      "Pizza lunch",
		Date:             "2025-01-15",
		AdditionalAmount: 3.00,
	}
}

func createInvalidAmountExpense() *models.Expense {
	expense := createValidExpense()
	expense.Amount = -10.0
	return expense
}

func createMissingCategoryExpense() *models.Expense {
	expense := createValidExpense()
	expense.Category = ""
	return expense
}

func createMissingSubcategoryExpense() *models.Expense {
	expense := createValidExpense()
	expense.Subcategory = ""
	return expense
}

func createInvalidDateExpense() *models.Expense {
	expense := createValidExpense()
	expense.Date = "invalid-date"
	return expense
}

func createExpenseList() []models.Expense {
	return []models.Expense{
		{
			ID:               1,
			Amount:           25.50,
			Category:         "Food",
			Subcategory:      "Lunch",
			Date:             "2025-01-15",
			AdditionalAmount: 3.00,
			CreatedAt:        "2025-01-15 12:00:00",
		},
		{
			ID:               2,
			Amount:           45.00,
			Category:         "Transportation",
			Subcategory:      "Gas",
			Date:             "2025-01-20",
			AdditionalAmount: 0,
			CreatedAt:        "2025-01-20 10:30:00",
		},
	}
}

// CreateExpense Tests

func TestCreateExpense_ValidExpense(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	expense := createValidExpense()

	mockRepo.EXPECT().Create(expense).Return(nil).Once()

	// Act
	err := service.CreateExpense(expense)

	// Assert
	assert.NoError(t, err)
}

func TestCreateExpense_ZeroAmount(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	expense := createValidExpense()
	expense.Amount = 0

	// Act
	err := service.CreateExpense(expense)

	// Assert
	assert.Error(t, err)
	assert.Equal(t, "amount must be greater than 0", err.Error())
}

func TestCreateExpense_NegativeAmount(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	expense := createInvalidAmountExpense()

	// Act
	err := service.CreateExpense(expense)

	// Assert
	assert.Error(t, err)
	assert.Equal(t, "amount must be greater than 0", err.Error())
}

func TestCreateExpense_MissingCategory(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	expense := createMissingCategoryExpense()

	// Act
	err := service.CreateExpense(expense)

	// Assert
	assert.Error(t, err)
	assert.Equal(t, "category is required", err.Error())
}

func TestCreateExpense_MissingSubcategory(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	expense := createMissingSubcategoryExpense()

	// Act
	err := service.CreateExpense(expense)

	// Assert
	assert.Error(t, err)
	assert.Equal(t, "subcategory is required", err.Error())
}

func TestCreateExpense_InvalidDateFormat(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	expense := createInvalidDateExpense()

	// Act
	err := service.CreateExpense(expense)

	// Assert
	assert.Error(t, err)
	assert.Equal(t, "date must be in YYYY-MM-DD format", err.Error())
}

func TestCreateExpense_InvalidDateValue(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	expense := createValidExpense()
	expense.Date = "2025-13-45" // Invalid month and day

	// Act
	err := service.CreateExpense(expense)

	// Assert
	assert.Error(t, err)
	assert.Equal(t, "date must be in YYYY-MM-DD format", err.Error())
}

func TestCreateExpense_RepositoryError(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	expense := createValidExpense()
	expectedError := errors.New("database connection failed")

	mockRepo.EXPECT().Create(expense).Return(expectedError).Once()

	// Act
	err := service.CreateExpense(expense)

	// Assert
	assert.Error(t, err)
	assert.Equal(t, expectedError, err)
}

// GetExpenses Tests

func TestGetExpenses_NoFilters(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	filters := map[string]string{}
	expectedExpenses := createExpenseList()

	mockRepo.EXPECT().GetAll(filters).Return(expectedExpenses, nil).Once()

	// Act
	expenses, err := service.GetExpenses(filters)

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, expectedExpenses, expenses)
	assert.Len(t, expenses, 2)
}

func TestGetExpenses_WithValidFilters(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	filters := map[string]string{
		"month":       "1",
		"year":        "2025",
		"category":    "Food",
		"subcategory": "Lunch",
	}
	expectedExpenses := createExpenseList()[:1] // First expense only

	mockRepo.EXPECT().GetAll(filters).Return(expectedExpenses, nil).Once()

	// Act
	expenses, err := service.GetExpenses(filters)

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, expectedExpenses, expenses)
	assert.Len(t, expenses, 1)
}

func TestGetExpenses_InvalidMonth(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	filters := map[string]string{
		"month": "13", // Invalid month
		"year":  "2025",
	}

	// Act
	expenses, err := service.GetExpenses(filters)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, expenses)
	assert.Equal(t, "invalid month parameter: must be between 1 and 12", err.Error())
}

func TestGetExpenses_InvalidMonthZero(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	filters := map[string]string{
		"month": "0", // Invalid month
		"year":  "2025",
	}

	// Act
	expenses, err := service.GetExpenses(filters)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, expenses)
	assert.Equal(t, "invalid month parameter: must be between 1 and 12", err.Error())
}

func TestGetExpenses_InvalidMonthNonNumeric(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	filters := map[string]string{
		"month": "abc", // Non-numeric month
		"year":  "2025",
	}

	// Act
	expenses, err := service.GetExpenses(filters)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, expenses)
	assert.Equal(t, "invalid month parameter: must be between 1 and 12", err.Error())
}

func TestGetExpenses_InvalidYear(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	filters := map[string]string{
		"month": "1",
		"year":  "1800", // Invalid year
	}

	// Act
	expenses, err := service.GetExpenses(filters)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, expenses)
	assert.Equal(t, "invalid year parameter: must be between 1900 and 2100", err.Error())
}

func TestGetExpenses_InvalidYearFuture(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	filters := map[string]string{
		"month": "1",
		"year":  "2200", // Invalid year
	}

	// Act
	expenses, err := service.GetExpenses(filters)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, expenses)
	assert.Equal(t, "invalid year parameter: must be between 1900 and 2100", err.Error())
}

func TestGetExpenses_InvalidYearNonNumeric(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	filters := map[string]string{
		"month": "1",
		"year":  "abc", // Non-numeric year
	}

	// Act
	expenses, err := service.GetExpenses(filters)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, expenses)
	assert.Equal(t, "invalid year parameter: must be between 1900 and 2100", err.Error())
}

func TestGetExpenses_RepositoryError(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	filters := map[string]string{}
	expectedError := errors.New("database query failed")

	mockRepo.EXPECT().GetAll(filters).Return(nil, expectedError).Once()

	// Act
	expenses, err := service.GetExpenses(filters)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, expenses)
	assert.Equal(t, expectedError, err)
}

// ExportExpensesToExcel Tests

func TestExportExpensesToExcel_ValidParameters(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	monthStr := "1"
	yearStr := "2025"
	filters := map[string]string{
		"month": monthStr,
		"year":  yearStr,
	}
	expectedExpenses := createExpenseList()

	mockRepo.EXPECT().GetAll(filters).Return(expectedExpenses, nil).Once()

	// Act
	excelFile, err := service.ExportExpensesToExcel(monthStr, yearStr)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, excelFile)
	excelFile.Close()
}

func TestExportExpensesToExcel_EmptyMonth(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	monthStr := ""
	yearStr := "2025"

	// Act
	excelFile, err := service.ExportExpensesToExcel(monthStr, yearStr)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, excelFile)
	assert.Equal(t, "month and year parameters are required", err.Error())
}

func TestExportExpensesToExcel_EmptyYear(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	monthStr := "1"
	yearStr := ""

	// Act
	excelFile, err := service.ExportExpensesToExcel(monthStr, yearStr)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, excelFile)
	assert.Equal(t, "month and year parameters are required", err.Error())
}

func TestExportExpensesToExcel_BothEmpty(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	monthStr := ""
	yearStr := ""

	// Act
	excelFile, err := service.ExportExpensesToExcel(monthStr, yearStr)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, excelFile)
	assert.Equal(t, "month and year parameters are required", err.Error())
}

func TestExportExpensesToExcel_InvalidMonth(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	monthStr := "13"
	yearStr := "2025"

	// Act
	excelFile, err := service.ExportExpensesToExcel(monthStr, yearStr)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, excelFile)
	assert.Contains(t, err.Error(), "invalid month")
}

func TestExportExpensesToExcel_InvalidYear(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	monthStr := "1"
	yearStr := "1800"

	// Act
	excelFile, err := service.ExportExpensesToExcel(monthStr, yearStr)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, excelFile)
	assert.Contains(t, err.Error(), "invalid year")
}

func TestExportExpensesToExcel_RepositoryError(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	monthStr := "1"
	yearStr := "2025"
	filters := map[string]string{
		"month": monthStr,
		"year":  yearStr,
	}
	expectedError := errors.New("database query failed")

	mockRepo.EXPECT().GetAll(filters).Return(nil, expectedError).Once()

	// Act
	excelFile, err := service.ExportExpensesToExcel(monthStr, yearStr)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, excelFile)
	assert.Contains(t, err.Error(), "failed to retrieve expenses")
	assert.Contains(t, err.Error(), expectedError.Error())
}

func TestExportExpensesToExcel_EmptyExpenseList(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	monthStr := "1"
	yearStr := "2025"
	filters := map[string]string{
		"month": monthStr,
		"year":  yearStr,
	}
	emptyExpenses := []models.Expense{}

	mockRepo.EXPECT().GetAll(filters).Return(emptyExpenses, nil).Once()

	// Act
	excelFile, err := service.ExportExpensesToExcel(monthStr, yearStr)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, excelFile)
	excelFile.Close()
}

// Edge case tests

func TestCreateExpense_EdgeCaseAmounts(t *testing.T) {
	testCases := []struct {
		name     string
		amount   float64
		expected bool
	}{
		{"VerySmallAmount", 0.01, true},
		{"LargeAmount", 999999.99, true},
		{"ExactlyZero", 0.0, false},
		{"NegativeSmall", -0.01, false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Arrange
			mockRepo := mocks.NewMockExpenseRepository(t)
			service := NewExpenseService(mockRepo)
			expense := createValidExpense()
			expense.Amount = tc.amount

			if tc.expected {
				mockRepo.EXPECT().Create(expense).Return(nil).Once()
			}

			// Act
			err := service.CreateExpense(expense)

			// Assert
			if tc.expected {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
				assert.Equal(t, "amount must be greater than 0", err.Error())
			}
		})
	}
}

func TestGetExpenses_EdgeCaseMonths(t *testing.T) {
	testCases := []struct {
		name     string
		month    string
		expected bool
	}{
		{"ValidMonth1", "1", true},
		{"ValidMonth12", "12", true},
		{"InvalidMonth0", "0", false},
		{"InvalidMonth13", "13", false},
		{"InvalidMonthNegative", "-1", false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Arrange
			mockRepo := mocks.NewMockExpenseRepository(t)
			service := NewExpenseService(mockRepo)
			filters := map[string]string{
				"month": tc.month,
				"year":  "2025",
			}

			if tc.expected {
				mockRepo.EXPECT().GetAll(filters).Return([]models.Expense{}, nil).Once()
			}

			// Act
			expenses, err := service.GetExpenses(filters)

			// Assert
			if tc.expected {
				assert.NoError(t, err)
				assert.NotNil(t, expenses)
			} else {
				assert.Error(t, err)
				assert.Nil(t, expenses)
			}
		})
	}
}

// GetMonthlySummary Tests

func TestGetMonthlySummary_ValidParameters(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	monthStr := "1"
	yearStr := "2025"

	expectedSummary := &models.MonthlySummary{
		Month:     1,
		Year:      2025,
		MonthName: "January",
		Total:     150.75,
		WeeklyBreakdown: []models.WeeklySummary{
			{WeekNumber: 1, StartDate: "2025-01-01", EndDate: "2025-01-07", Total: 75.25},
			{WeekNumber: 2, StartDate: "2025-01-08", EndDate: "2025-01-14", Total: 75.50},
		},
	}

	mockRepo.EXPECT().GetMonthlySummary(1, 2025).Return(expectedSummary, nil).Once()

	// Act
	summary, err := service.GetMonthlySummary(monthStr, yearStr)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, summary)
	assert.Equal(t, expectedSummary, summary)
}

func TestGetMonthlySummary_EmptyMonth(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	monthStr := ""
	yearStr := "2025"

	// Act
	summary, err := service.GetMonthlySummary(monthStr, yearStr)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, summary)
	assert.Equal(t, "month and year parameters are required", err.Error())
}

func TestGetMonthlySummary_EmptyYear(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	monthStr := "1"
	yearStr := ""

	// Act
	summary, err := service.GetMonthlySummary(monthStr, yearStr)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, summary)
	assert.Equal(t, "month and year parameters are required", err.Error())
}

func TestGetMonthlySummary_BothEmpty(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	monthStr := ""
	yearStr := ""

	// Act
	summary, err := service.GetMonthlySummary(monthStr, yearStr)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, summary)
	assert.Equal(t, "month and year parameters are required", err.Error())
}

func TestGetMonthlySummary_InvalidMonth(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	monthStr := "13"
	yearStr := "2025"

	// Act
	summary, err := service.GetMonthlySummary(monthStr, yearStr)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, summary)
	assert.Contains(t, err.Error(), "invalid month")
}

func TestGetMonthlySummary_InvalidYear(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	monthStr := "1"
	yearStr := "1800"

	// Act
	summary, err := service.GetMonthlySummary(monthStr, yearStr)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, summary)
	assert.Contains(t, err.Error(), "invalid year")
}

func TestGetMonthlySummary_RepositoryError(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	monthStr := "1"
	yearStr := "2025"
	expectedError := errors.New("database query failed")

	mockRepo.EXPECT().GetMonthlySummary(1, 2025).Return(nil, expectedError).Once()

	// Act
	summary, err := service.GetMonthlySummary(monthStr, yearStr)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, summary)
	assert.Contains(t, err.Error(), "failed to retrieve monthly summary")
	assert.Contains(t, err.Error(), expectedError.Error())
}

// GetYearlySummary Tests

func TestGetYearlySummary_ValidParameters(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	yearStr := "2025"

	expectedSummary := &models.YearlySummary{
		Year:  2025,
		Total: 1800.50,
		MonthlyBreakdown: []models.MonthlySummaryItem{
			{Month: 1, MonthName: "January", Total: 150.25},
			{Month: 2, MonthName: "February", Total: 200.75},
			{Month: 3, MonthName: "March", Total: 175.50},
		},
	}

	mockRepo.EXPECT().GetYearlySummary(2025).Return(expectedSummary, nil).Once()

	// Act
	summary, err := service.GetYearlySummary(yearStr)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, summary)
	assert.Equal(t, expectedSummary, summary)
}

func TestGetYearlySummary_EmptyYear(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	yearStr := ""

	// Act
	summary, err := service.GetYearlySummary(yearStr)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, summary)
	assert.Equal(t, "year parameter is required", err.Error())
}

func TestGetYearlySummary_InvalidYear(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	yearStr := "1800"

	// Act
	summary, err := service.GetYearlySummary(yearStr)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, summary)
	assert.Contains(t, err.Error(), "invalid year parameter")
}

func TestGetYearlySummary_NonNumericYear(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	yearStr := "abc"

	// Act
	summary, err := service.GetYearlySummary(yearStr)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, summary)
	assert.Contains(t, err.Error(), "invalid year parameter")
}

func TestGetYearlySummary_RepositoryError(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := NewExpenseService(mockRepo)
	yearStr := "2025"
	expectedError := errors.New("database query failed")

	mockRepo.EXPECT().GetYearlySummary(2025).Return(nil, expectedError).Once()

	// Act
	summary, err := service.GetYearlySummary(yearStr)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, summary)
	assert.Contains(t, err.Error(), "failed to retrieve yearly summary")
	assert.Contains(t, err.Error(), expectedError.Error())
}
