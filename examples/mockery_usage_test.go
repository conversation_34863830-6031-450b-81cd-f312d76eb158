package examples

import (
	"errors"
	"go-expense/handlers"
	"go-expense/models"
	"go-expense/models/mocks"
	"go-expense/services"
	serviceMocks "go-expense/services/mocks"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/xuri/excelize/v2"
)

// TestExpenseService_CreateExpense_Success demonstrates testing service with repository mock
func TestExpenseService_CreateExpense_Success(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := services.NewExpenseService(mockRepo)

	expense := &models.Expense{
		Amount:      25.50,
		Category:    "Food",
		Subcategory: "Lunch",
		Date:        "2025-01-15",
	}

	// Set up expectations using Mockery's fluent API
	mockRepo.EXPECT().Create(expense).Return(nil).Once()

	// Act
	err := service.CreateExpense(expense)

	// Assert
	assert.NoError(t, err)
	// mockRepo.AssertExpectations(t) is called automatically by Mockery
}

// TestExpenseService_CreateExpense_RepositoryError demonstrates error testing
func TestExpenseService_CreateExpense_RepositoryError(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := services.NewExpenseService(mockRepo)

	expense := &models.Expense{
		Amount:      25.50,
		Category:    "Food",
		Subcategory: "Lunch",
		Date:        "2025-01-15",
	}

	expectedError := errors.New("database connection failed")

	// Set up expectations
	mockRepo.EXPECT().Create(expense).Return(expectedError).Once()

	// Act
	err := service.CreateExpense(expense)

	// Assert
	assert.Error(t, err)
	assert.Equal(t, expectedError, err)
}

// TestExpenseService_CreateExpense_ValidationError demonstrates validation testing
func TestExpenseService_CreateExpense_ValidationError(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := services.NewExpenseService(mockRepo)

	expense := &models.Expense{
		Amount:      -10, // Invalid amount
		Category:    "Food",
		Subcategory: "Lunch",
		Date:        "2025-01-15",
	}

	// No expectations set on mockRepo because validation should fail before repository call

	// Act
	err := service.CreateExpense(expense)

	// Assert
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "amount must be greater than 0")
	// Repository should not have been called
}

// TestExpenseService_GetExpenses_Success demonstrates testing with return values
func TestExpenseService_GetExpenses_Success(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := services.NewExpenseService(mockRepo)

	filters := map[string]string{
		"month": "1",
		"year":  "2025",
	}

	expectedExpenses := []models.Expense{
		{
			ID:          1,
			Amount:      25.50,
			Category:    "Food",
			Subcategory: "Lunch",
			Date:        "2025-01-15",
		},
		{
			ID:          2,
			Amount:      30.00,
			Category:    "Transport",
			Subcategory: "Gas",
			Date:        "2025-01-20",
		},
	}

	// Set up expectations with return values
	mockRepo.EXPECT().GetAll(filters).Return(expectedExpenses, nil).Once()

	// Act
	expenses, err := service.GetExpenses(filters)

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, expectedExpenses, expenses)
	assert.Len(t, expenses, 2)
}

// TestExpenseService_GetExpenses_WithArgumentMatching demonstrates argument matching
func TestExpenseService_GetExpenses_WithArgumentMatching(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := services.NewExpenseService(mockRepo)

	expectedExpenses := []models.Expense{
		{ID: 1, Amount: 25.50, Category: "Food"},
	}

	// Use mock.MatchedBy for custom argument matching
	mockRepo.EXPECT().GetAll(mock.MatchedBy(func(filters map[string]string) bool {
		// Custom validation logic
		return filters["category"] == "Food"
	})).Return(expectedExpenses, nil).Once()

	// Act
	filters := map[string]string{"category": "Food"}
	expenses, err := service.GetExpenses(filters)

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, expectedExpenses, expenses)
}

// TestExpenseHandler_CreateExpense_Success demonstrates testing handlers with service mock
func TestExpenseHandler_CreateExpense_Success(t *testing.T) {
	// Arrange
	mockService := serviceMocks.NewMockExpenseService(t)
	handler := handlers.NewExpenseHandler(mockService)

	// Set up expectations
	mockService.EXPECT().CreateExpense(mock.AnythingOfType("*models.Expense")).
		Return(nil).
		Run(func(expense *models.Expense) {
			// Simulate setting ID like real service would
			expense.ID = 1
		}).
		Once()

	// Create HTTP request
	body := `{"amount":25.50,"category":"Food","subcategory":"Lunch","date":"2025-01-15"}`
	req := httptest.NewRequest("POST", "/expenses", strings.NewReader(body))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	// Act
	handler.CreateExpense(w, req)

	// Assert
	assert.Equal(t, http.StatusCreated, w.Code)
	assert.Contains(t, w.Body.String(), `"id":1`)
}

// TestExpenseHandler_CreateExpense_ServiceError demonstrates error handling in handlers
func TestExpenseHandler_CreateExpense_ServiceError(t *testing.T) {
	// Arrange
	mockService := serviceMocks.NewMockExpenseService(t)
	handler := handlers.NewExpenseHandler(mockService)

	expectedError := errors.New("validation failed")

	// Set up expectations
	mockService.EXPECT().CreateExpense(mock.AnythingOfType("*models.Expense")).
		Return(expectedError).
		Once()

	// Create HTTP request
	body := `{"amount":25.50,"category":"Food","subcategory":"Lunch","date":"2025-01-15"}`
	req := httptest.NewRequest("POST", "/expenses", strings.NewReader(body))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	// Act
	handler.CreateExpense(w, req)

	// Assert
	assert.Equal(t, http.StatusBadRequest, w.Code)
	assert.Contains(t, w.Body.String(), "validation failed")
}

// TestExpenseHandler_ExportExpenses_Success demonstrates testing Excel export
func TestExpenseHandler_ExportExpenses_Success(t *testing.T) {
	// Arrange
	mockService := serviceMocks.NewMockExpenseService(t)
	handler := handlers.NewExpenseHandler(mockService)

	// Create a mock Excel file
	mockExcelFile := excelize.NewFile()
	defer mockExcelFile.Close()

	// Set up expectations
	mockService.EXPECT().ExportExpensesToExcel("1", "2025").
		Return(mockExcelFile, nil).
		Once()

	// Create HTTP request
	req := httptest.NewRequest("GET", "/expenses/export?month=1&year=2025", nil)
	w := httptest.NewRecorder()

	// Act
	handler.ExportExpenses(w, req)

	// Assert
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", w.Header().Get("Content-Type"))
	assert.Contains(t, w.Header().Get("Content-Disposition"), "expenses_2025_1.xlsx")
}

// TestExpenseService_Multiple_Calls demonstrates testing multiple method calls
func TestExpenseService_Multiple_Calls(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := services.NewExpenseService(mockRepo)

	expense1 := &models.Expense{Amount: 25.50, Category: "Food", Subcategory: "Lunch", Date: "2025-01-15"}
	expense2 := &models.Expense{Amount: 30.00, Category: "Transport", Subcategory: "Gas", Date: "2025-01-20"}

	// Set up expectations for multiple calls
	mockRepo.EXPECT().Create(expense1).Return(nil).Once()
	mockRepo.EXPECT().Create(expense2).Return(nil).Once()
	mockRepo.EXPECT().GetAll(mock.Anything).Return([]models.Expense{*expense1, *expense2}, nil).Once()

	// Act
	err1 := service.CreateExpense(expense1)
	err2 := service.CreateExpense(expense2)
	expenses, err3 := service.GetExpenses(map[string]string{})

	// Assert
	assert.NoError(t, err1)
	assert.NoError(t, err2)
	assert.NoError(t, err3)
	assert.Len(t, expenses, 2)
}

// TestExpenseService_WithRunFunction demonstrates using Run function for side effects
func TestExpenseService_WithRunFunction(t *testing.T) {
	// Arrange
	mockRepo := mocks.NewMockExpenseRepository(t)
	service := services.NewExpenseService(mockRepo)

	expense := &models.Expense{
		Amount:      25.50,
		Category:    "Food",
		Subcategory: "Lunch",
		Date:        "2025-01-15",
	}

	// Set up expectations with Run function to simulate side effects
	mockRepo.EXPECT().Create(expense).
		Run(func(exp *models.Expense) {
			// Simulate what real repository would do
			exp.ID = 42
			exp.CreatedAt = "2025-01-15 12:00:00"
		}).
		Return(nil).
		Once()

	// Act
	err := service.CreateExpense(expense)

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, 42, expense.ID)
	assert.Equal(t, "2025-01-15 12:00:00", expense.CreatedAt)
}
