package utils

import (
	"fmt"
	"go-expense/models"
	"strconv"

	"github.com/xuri/excelize/v2"
)

// ExportToExcel creates an Excel file with the given expenses
func ExportToExcel(expenses []models.Expense, month, year string) (*excelize.File, error) {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()

	// Create a new worksheet
	sheetName := fmt.Sprintf("Expenses_%s_%s", month, year)
	index, err := f.NewSheet(sheetName)
	if err != nil {
		return nil, err
	}

	// Set headers
	headers := []string{"ID", "Amount", "Category", "Subcategory", "Description", "Date", "Additional Amount", "Total", "Created At"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		f.SetCellValue(sheetName, cell, header)
	}

	// Style headers
	headerStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#E0E0E0"},
			Pattern: 1,
		},
	})
	if err != nil {
		return nil, err
	}

	// Apply header style
	f.SetCellStyle(sheetName, "A1", fmt.Sprintf("%c1", 'A'+len(headers)-1), headerStyle)

	// Add data rows
	for i, expense := range expenses {
		row := i + 2 // Start from row 2 (after headers)
		
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), expense.ID)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), expense.Amount)
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), expense.Category)
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), expense.Subcategory)
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), expense.Description)
		f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), expense.Date)
		f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), expense.AdditionalAmount)
		
		// Calculate total (amount + additional_amount)
		total := expense.Amount + expense.AdditionalAmount
		f.SetCellValue(sheetName, fmt.Sprintf("H%d", row), total)
		
		f.SetCellValue(sheetName, fmt.Sprintf("I%d", row), expense.CreatedAt)
	}

	// Add summary row
	if len(expenses) > 0 {
		summaryRow := len(expenses) + 3
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", summaryRow), "TOTAL:")
		
		// Calculate totals
		var totalAmount, totalAdditional, grandTotal float64
		for _, expense := range expenses {
			totalAmount += expense.Amount
			totalAdditional += expense.AdditionalAmount
			grandTotal += expense.Amount + expense.AdditionalAmount
		}
		
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", summaryRow), totalAmount)
		f.SetCellValue(sheetName, fmt.Sprintf("G%d", summaryRow), totalAdditional)
		f.SetCellValue(sheetName, fmt.Sprintf("H%d", summaryRow), grandTotal)
		
		// Style summary row
		summaryStyle, err := f.NewStyle(&excelize.Style{
			Font: &excelize.Font{
				Bold: true,
			},
		})
		if err == nil {
			f.SetCellStyle(sheetName, fmt.Sprintf("A%d", summaryRow), fmt.Sprintf("I%d", summaryRow), summaryStyle)
		}
	}

	// Auto-fit columns
	cols := []string{"A", "B", "C", "D", "E", "F", "G", "H", "I"}
	for _, col := range cols {
		f.SetColWidth(sheetName, col, col, 15)
	}

	// Set active sheet
	f.SetActiveSheet(index)
	
	// Delete default sheet if it exists and is not our sheet
	if f.GetSheetName(0) == "Sheet1" && sheetName != "Sheet1" {
		f.DeleteSheet("Sheet1")
	}

	return f, nil
}

// ValidateMonthYear validates month and year parameters
func ValidateMonthYear(monthStr, yearStr string) (int, int, error) {
	month, err := strconv.Atoi(monthStr)
	if err != nil || month < 1 || month > 12 {
		return 0, 0, fmt.Errorf("invalid month: must be between 1 and 12")
	}

	year, err := strconv.Atoi(yearStr)
	if err != nil || year < 1900 || year > 2100 {
		return 0, 0, fmt.Errorf("invalid year: must be between 1900 and 2100")
	}

	return month, year, nil
}
