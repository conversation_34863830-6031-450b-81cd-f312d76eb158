{{define "summary-content"}}
<div class="card">
    <h1>Expense Summary</h1>
    
    <form method="GET" action="/ui/summary">
        <div class="filters">
            <div class="form-group">
                <label for="month">Month</label>
                <select name="month" id="month" class="auto-submit">
                    <option value="">Select Month</option>
                    <option value="1" {{if eq .Filters.Month "1"}}selected{{end}}>January</option>
                    <option value="2" {{if eq .Filters.Month "2"}}selected{{end}}>February</option>
                    <option value="3" {{if eq .Filters.Month "3"}}selected{{end}}>March</option>
                    <option value="4" {{if eq .Filters.Month "4"}}selected{{end}}>April</option>
                    <option value="5" {{if eq .Filters.Month "5"}}selected{{end}}>May</option>
                    <option value="6" {{if eq .Filters.Month "6"}}selected{{end}}>June</option>
                    <option value="7" {{if eq .Filters.Month "7"}}selected{{end}}>July</option>
                    <option value="8" {{if eq .Filters.Month "8"}}selected{{end}}>August</option>
                    <option value="9" {{if eq .Filters.Month "9"}}selected{{end}}>September</option>
                    <option value="10" {{if eq .Filters.Month "10"}}selected{{end}}>October</option>
                    <option value="11" {{if eq .Filters.Month "11"}}selected{{end}}>November</option>
                    <option value="12" {{if eq .Filters.Month "12"}}selected{{end}}>December</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="year">Year *</label>
                <select name="year" id="year" class="auto-submit" required>
                    <option value="">Tahun</option>
                    <option value="2024" {{if eq .Filters.Year "2024"}}selected{{end}}>2024</option>
                    <option value="2025" {{if eq .Filters.Year "2025"}}selected{{end}}>2025</option>
                    <option value="2026" {{if eq .Filters.Year "2026"}}selected{{end}}>2026</option>
                </select>
            </div>
            
            <div class="form-group">
                <label>&nbsp;</label>
                <button type="submit" class="btn">Generate Summary</button>
            </div>
        </div>
    </form>
</div>

{{if .Summary}}
<div class="card">
    <h2>{{if .Filters.Month}}Monthly Summary - {{.MonthName}} {{.Filters.Year}}{{else}}Yearly Summary - {{.Filters.Year}}{{end}}</h2>

    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value">{{formatCurrency .Summary.TotalExpense}}</div>
            <div class="stat-label">Total Expense</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{formatCurrency .Summary.TotalAmount}}</div>
            <div class="stat-label">Total Amount</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{formatCurrency .Summary.TotalAdditionalAmount}}</div>
            <div class="stat-label">Total Additional Amount</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{formatCurrency .Summary.AverageAmount}}</div>
            <div class="stat-label">Average Amount</div>
        </div>
    </div>
</div>

{{if and (not .Filters.Month) .Summary.MonthlyBreakdown}}
<div class="card">
    <h2>Monthly Breakdown</h2>
    <table class="table">
        <thead>
            <tr>
                <th>Month</th>
                <th>Amount</th>
                <th>Count</th>
                <th>Average</th>
            </tr>
        </thead>
        <tbody>
            {{range .Summary.MonthlyBreakdown}}
            <tr>
                <td>{{.MonthName}}</td>
                <td>{{formatCurrency .Amount}}</td>
                <td>{{.Count}}</td>
                <td>{{formatCurrency .Average}}</td>
            </tr>
            {{end}}
        </tbody>
    </table>
</div>
{{end}}

{{if .Summary.CategoryBreakdown}}
<div class="card">
    <h2>Category & Subcategory Breakdown</h2>
    <table class="table">
        <thead>
            <tr>
                <th>Category / Subcategory</th>
                <th>Amount</th>
                <th>Count</th>
                <th>Average</th>
                <th>Percentage</th>
            </tr>
        </thead>
        <tbody>
            {{range .Summary.CategoryBreakdown}}
            <tr style="background-color: #f8f9fa; font-weight: bold;">
                <td><strong>{{.Category}}</strong></td>
                <td>{{formatCurrency .Amount}}</td>
                <td>{{.Count}}</td>
                <td>{{formatCurrency .Average}}</td>
                <td>{{printf "%.1f" .Percentage}}%</td>
            </tr>
            {{range .Subcategories}}
            <tr>
                <td style="padding-left: 2rem; color: #6c757d;">└─ {{.Subcategory}}</td>
                <td>{{formatCurrency .Amount}}</td>
                <td>{{.Count}}</td>
                <td>{{formatCurrency .Average}}</td>
                <td>{{printf "%.1f" .Percentage}}%</td>
            </tr>
            {{end}}
            {{end}}
        </tbody>
    </table>
</div>
{{end}}

{{if and .Filters.Month .Summary.WeeklyBreakdown}}
<div class="card">
    <h2>Weekly Breakdown</h2>
    <table class="table">
        <thead>
            <tr>
                <th>Week</th>
                <th>Amount</th>
                <th>Count</th>
                <th>Average</th>
            </tr>
        </thead>
        <tbody>
            {{range .Summary.WeeklyBreakdown}}
            <tr>
                <td>Week {{.Week}}</td>
                <td>{{formatCurrency .Amount}}</td>
                <td>{{.Count}}</td>
                <td>{{formatCurrency .Average}}</td>
            </tr>
            {{end}}
        </tbody>
    </table>
</div>
{{end}}



<div class="card">
    <h2>Export Options</h2>
    <div style="display: flex; gap: 1rem;">
        {{if .Filters.Month}}
        <a href="/expenses/export?month={{.Filters.Month}}&year={{.Filters.Year}}" class="btn">
            Export Monthly Data to Excel
        </a>
        {{end}}
        <a href="/expenses/summary/{{if .Filters.Month}}monthly?month={{.Filters.Month}}&year={{.Filters.Year}}{{else}}yearly?year={{.Filters.Year}}{{end}}" class="btn btn-secondary">
            View Raw JSON Data
        </a>
    </div>
</div>

{{else if .Filters.Year}}
<div class="card">
    <div style="text-align: center; padding: 2rem;">
        <h2>No data found</h2>
        <p>No expenses found for {{if .Filters.Month}}{{.MonthName}} {{end}}{{.Filters.Year}}.</p>
        <a href="/ui/add" class="btn">Add an expense</a>
    </div>
</div>
{{end}}

<script>
    // Set current year as default if no year is selected
    document.addEventListener('DOMContentLoaded', function() {
        const yearSelect = document.getElementById('year');
        if (!yearSelect.value) {
            const currentYear = new Date().getFullYear();
            yearSelect.value = currentYear.toString();
        }
    });
</script>
{{end}}
