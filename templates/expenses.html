{{define "expenses-content"}}
<div class="card">
    <h1>Expenses</h1>
    
    <form method="GET" action="/ui/expenses">
        <div class="filters">
            <div class="form-group">
                <label for="month">Bulan</label>
                <select name="month" id="month" class="auto-submit">
                    <option value="">All Months</option>
                    <option value="1" {{if eq .Filters.Month "1"}}selected{{end}}>January</option>
                    <option value="2" {{if eq .Filters.Month "2"}}selected{{end}}>February</option>
                    <option value="3" {{if eq .Filters.Month "3"}}selected{{end}}>March</option>
                    <option value="4" {{if eq .Filters.Month "4"}}selected{{end}}>April</option>
                    <option value="5" {{if eq .Filters.Month "5"}}selected{{end}}>May</option>
                    <option value="6" {{if eq .Filters.Month "6"}}selected{{end}}>June</option>
                    <option value="7" {{if eq .Filters.Month "7"}}selected{{end}}>July</option>
                    <option value="8" {{if eq .Filters.Month "8"}}selected{{end}}>August</option>
                    <option value="9" {{if eq .Filters.Month "9"}}selected{{end}}>September</option>
                    <option value="10" {{if eq .Filters.Month "10"}}selected{{end}}>October</option>
                    <option value="11" {{if eq .Filters.Month "11"}}selected{{end}}>November</option>
                    <option value="12" {{if eq .Filters.Month "12"}}selected{{end}}>December</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="year">Year</label>
                <select name="year" id="year" class="auto-submit">
                    <option value="">All Years</option>
                    <option value="2024" {{if eq .Filters.Year "2024"}}selected{{end}}>2024</option>
                    <option value="2025" {{if eq .Filters.Year "2025"}}selected{{end}}>2025</option>
                    <option value="2026" {{if eq .Filters.Year "2026"}}selected{{end}}>2026</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="category">Category</label>
                <select name="category" id="category" class="auto-submit">
                    <option value="">All Categories</option>
                    {{range .Categories}}
                    <option value="{{.}}" {{if eq $.Filters.Category .}}selected{{end}}>{{.}}</option>
                    {{end}}
                </select>
            </div>
            
            <div class="form-group">
                <label for="subcategory">Subcategory</label>
                <select name="subcategory" id="subcategory" class="auto-submit">
                    <option value="">All Subcategories</option>
                    {{range .Subcategories}}
                    <option value="{{.}}" {{if eq $.Filters.Subcategory .}}selected{{end}}>{{.}}</option>
                    {{end}}
                </select>
            </div>
            
            <div class="form-group">
                <label>&nbsp;</label>
                <button type="submit" class="btn">Filter</button>
            </div>
        </div>
    </form>
</div>

<div class="card">
    {{if .Summary}}
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value">{{formatCurrency .Summary.Total}}</div>
            <div class="stat-label">Total Amount</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{.Summary.Count}}</div>
            <div class="stat-label">Number of Expenses</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{formatCurrency .Summary.Average}}</div>
            <div class="stat-label">Average Amount</div>
        </div>
    </div>
    {{end}}
    
    {{if .Expenses}}
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
        <h2>Expense List ({{len .Expenses}} items)</h2>
        <a href="/ui/add" class="btn">Add New Expense</a>
    </div>
    
    <div style="overflow-x: auto;">
        <table class="table">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Description</th>
                    <th>Category</th>
                    <th>Subcategory</th>
                    <th>Amount</th>
                    <th>Additional</th>
                    <th>Total</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {{range .Expenses}}
                <tr>
                    <td>{{.Date}}</td>
                    <td>{{.Description}}</td>
                    <td>{{.Category}}</td>
                    <td>{{.Subcategory}}</td>
                    <td>{{formatCurrency .Amount}}</td>
                    <td>{{if .AdditionalAmount}}{{formatCurrency .AdditionalAmount}}{{else}}-{{end}}</td>
                    <td><strong>{{formatCurrency (add .Amount .AdditionalAmount)}}</strong></td>
                    <td>
                        <a href="/ui/edit/{{.ID}}" class="btn btn-secondary" style="font-size: 0.8rem; padding: 0.25rem 0.5rem;">Edit</a>
                    </td>
                </tr>
                {{end}}
            </tbody>
        </table>
    </div>
    
    {{if and .Filters.Month .Filters.Year}}
    <div style="margin-top: 1rem;">
        <a href="/expenses/export?month={{.Filters.Month}}&year={{.Filters.Year}}" class="btn btn-secondary">
            Export to Excel
        </a>
    </div>
    {{end}}
    
    {{else}}
    <div style="text-align: center; padding: 2rem;">
        <h2>No expenses found</h2>
        <p>{{if or .Filters.Month .Filters.Year .Filters.Category .Filters.Subcategory}}
            Try adjusting your filters or <a href="/ui/expenses">view all expenses</a>.
        {{else}}
            Get started by <a href="/ui/add">adding your first expense</a>!
        {{end}}</p>
    </div>
    {{end}}
</div>
{{end}}
