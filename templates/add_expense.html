{{define "add-expense-content"}}
<div class="card">
    <h1>{{if .IsEditMode}}Edit Expense{{else}}Add New Expense{{end}}</h1>

    <div id="notification-area">
        {{if .Success}}
        <div class="alert alert-success">
            {{if .IsEditMode}}
                Expense updated successfully! <a href="/ui/expenses">View all expenses</a> or continue editing below.
            {{else}}
                Expense added successfully! <a href="/ui/expenses">View all expenses</a> or add another one below.
            {{end}}
        </div>
        {{end}}

        {{if .Error}}
        <div class="alert alert-error">
            {{.Error}}
        </div>
        {{end}}
    </div>

    <form method="POST" action="{{if .IsEditMode}}/ui/edit/{{.ExpenseID}}{{else}}/ui/add{{end}}" hx-post="{{if .IsEditMode}}/ui/edit/{{.ExpenseID}}{{else}}/ui/add{{end}}" hx-target="#notification-area" hx-swap="innerHTML" hx-on::after-request="handleFormResponse(event)">
        <div id="form-container">
            <!-- Basic Information Group -->
            <div class="form-section">
                <h3 class="form-section-title">When & What</h3>

                <div class="form-group">
                    <label for="date">Date *</label>
                    <input type="date" name="date" id="date" required
                           value="{{.FormData.Date}}">
                    <small class="field-help">When did this expense occur?</small>
                </div>

                <div class="form-group">
                    <label for="description">Description *</label>
                    <input type="text" name="description" id="description" required
                           value="{{.FormData.Description}}" placeholder="e.g., Lunch at restaurant, Gas for car">
                    <small class="field-help">Brief description of what you spent money on</small>
                </div>
            </div>

            <!-- Category Information Group -->
            <div class="form-section">
                <h3 class="form-section-title">Category</h3>

                <div class="form-group">
                    <label for="category">Category *</label>
                    <select name="category" id="category" required>
                        <option value="">Choose a category</option>
                        <option value="Food" {{if eq .FormData.Category "Food"}}selected{{end}}>Food</option>
                        <option value="Transportation" {{if eq .FormData.Category "Transportation"}}selected{{end}}>Transportation</option>
                        <option value="Entertainment" {{if eq .FormData.Category "Entertainment"}}selected{{end}}>Entertainment</option>
                        <option value="Shopping" {{if eq .FormData.Category "Shopping"}}selected{{end}}>Shopping</option>
                        <option value="Bills" {{if eq .FormData.Category "Bills"}}selected{{end}}>Bills</option>
                        <option value="Healthcare" {{if eq .FormData.Category "Healthcare"}}selected{{end}}>Healthcare</option>
                        <option value="Education" {{if eq .FormData.Category "Education"}}selected{{end}}>Education</option>
                        <option value="Travel" {{if eq .FormData.Category "Travel"}}selected{{end}}>Travel</option>
                        <option value="Other" {{if eq .FormData.Category "Other"}}selected{{end}}>Other</option>
                    </select>
                    <small class="field-help">What type of expense is this?</small>
                </div>

                <div class="form-group">
                    <label for="subcategory">Subcategory <span class="optional">(optional)</span></label>
                    <input type="text" name="subcategory" id="subcategory"
                           value="{{.FormData.Subcategory}}" placeholder="e.g., Lunch, Gas, Movies">
                    <small class="field-help">More specific category for better tracking</small>
                </div>
            </div>

            <!-- Amount Information Group -->
            <div class="form-section">
                <h3 class="form-section-title">Amount</h3>

                <div class="form-group">
                    <label for="amount">Main Amount *</label>
                    <input type="number" step="0.01" name="amount" id="amount" required
                           value="{{.FormData.Amount}}" placeholder="0.00">
                    <small class="field-help">The primary cost of this expense</small>
                </div>

                <div class="form-group">
                    <label for="additional_amount">Additional Amount <span class="optional">(optional)</span></label>
                    <input type="number" step="0.01" name="additional_amount" id="additional_amount"
                           value="{{.FormData.AdditionalAmount}}" placeholder="0.00">
                    <small class="field-help">Tips, taxes, fees, or other additional costs</small>
                </div>

                <div class="total-display" id="total-display" style="display: none;">
                    <div class="total-label">Total Amount:</div>
                    <div class="total-value" id="total-value">Rp 0</div>
                </div>
            </div>

            <!-- Action Buttons - Mobile Optimized -->
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    {{if .IsEditMode}}Update Expense{{else}}Save Expense{{end}}
                </button>
                <a href="/ui/expenses" class="btn btn-secondary">Cancel</a>
            </div>
        </div>
    </form>
</div>

<div class="card">
    <h2>Quick Tips</h2>
    <ul style="margin-left: 1.5rem; line-height: 1.8;">
        <li>Use clear, descriptive names for your expenses</li>
        <li>Choose the most specific category available</li>
        <li>Add subcategories to better organize your spending</li>
        <li>Use additional amount for tips, taxes, or fees</li>
        <li>Set the correct date for accurate reporting</li>
    </ul>
</div>

<script>
    // Set today's date as default if no date is set
    document.addEventListener('DOMContentLoaded', function() {
        const dateInput = document.getElementById('date');
        if (!dateInput.value) {
            const today = new Date().toISOString().split('T')[0];
            dateInput.value = today;
        }

        // Focus on date field for better UX flow
        document.getElementById('date').focus();

        // Initialize total calculation
        updateTotal();
    });

    // Auto-calculate and display total when amounts change
    function updateTotal() {
        const amount = parseFloat(document.getElementById('amount').value) || 0;
        const additional = parseFloat(document.getElementById('additional_amount').value) || 0;
        const total = amount + additional;

        const totalDisplay = document.getElementById('total-display');
        const totalValue = document.getElementById('total-value');

        if (total > 0) {
            totalDisplay.style.display = 'block';
            totalValue.textContent = formatCurrency(total);
        } else {
            totalDisplay.style.display = 'none';
        }
    }

    // Format currency in Indonesian Rupiah style
    function formatCurrency(amount) {
        return 'Rp ' + amount.toLocaleString('id-ID', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        });
    }

    document.getElementById('amount').addEventListener('input', updateTotal);
    document.getElementById('additional_amount').addEventListener('input', updateTotal);

    // Handle HTMX form response
    function handleFormResponse(event) {
        // Check if the response contains a success message
        const response = event.detail.xhr.responseText;
        if (response.includes('alert-success')) {
            // Check if we're in edit mode by looking at the form action
            const form = document.querySelector('form');
            const isEditMode = form.action.includes('/ui/edit/');

            if (isEditMode) {
                // For edit mode, redirect to expenses page after a short delay
                setTimeout(() => {
                    window.location.href = '/ui/expenses';
                }, 1500); // 1.5 second delay to show the success message
            } else {
                // For add mode, clear the form for next entry
                document.getElementById('description').value = '';
                document.getElementById('category').value = '';
                document.getElementById('subcategory').value = '';
                document.getElementById('amount').value = '';
                document.getElementById('additional_amount').value = '';

                // Set today's date again
                const today = new Date().toISOString().split('T')[0];
                document.getElementById('date').value = today;

                // Focus back on date field for next entry
                document.getElementById('date').focus();

                // Update total display
                updateTotal();
            }
        }
    }
</script>
{{end}}

{{define "notification-partial"}}
{{if .Success}}
<div class="alert alert-success">
    {{if .IsEditMode}}
        Expense updated successfully! <a href="/ui/expenses">View all expenses</a> or continue editing below.
    {{else}}
        Expense added successfully! <a href="/ui/expenses">View all expenses</a> or add another one below.
    {{end}}
</div>
{{end}}

{{if .Error}}
<div class="alert alert-error">
    {{.Error}}
</div>
{{end}}
{{end}}
