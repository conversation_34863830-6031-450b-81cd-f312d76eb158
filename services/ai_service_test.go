package services

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestParseExpenseText_EmptyInput(t *testing.T) {
	// Arrange
	service := NewAIService("test-key")

	// Act
	result, err := service.ParseExpenseText("")

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "input text cannot be empty")
}

func TestValidateAndNormalize_InvalidAmount(t *testing.T) {
	// Arrange
	service := &AIService{}
	parsed := &ParsedExpense{
		Amount:     0,
		Category:   "Food",
		Date:       "2025-01-15",
		Confidence: 0.8,
	}

	// Act
	err := service.validateAndNormalize(parsed)

	// Assert
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "amount must be greater than 0")
}

func TestValidateAndNormalize_InvalidCategory(t *testing.T) {
	// Arrange
	service := &AIService{}
	parsed := &ParsedExpense{
		Amount:     25000,
		Category:   "InvalidCategory",
		Date:       "2025-01-15",
		Confidence: 0.8,
	}

	// Act
	err := service.validateAndNormalize(parsed)

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, "Other", parsed.Category) // Should default to "Other"
}

func TestValidateAndNormalize_LowConfidence(t *testing.T) {
	// Arrange
	service := &AIService{}
	parsed := &ParsedExpense{
		Amount:     25000,
		Category:   "Food",
		Date:       "2025-01-15",
		Confidence: 0.5, // Below threshold
	}

	// Act
	err := service.validateAndNormalize(parsed)

	// Assert
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "AI parsing confidence too low")
}

func TestValidateAndNormalize_ValidData(t *testing.T) {
	// Arrange
	service := &AIService{}
	parsed := &ParsedExpense{
		Amount:      25000,
		Category:    "Food",
		Subcategory: "Lunch",
		Description: "Kopi di Starbucks",
		Date:        "2025-01-15",
		Confidence:  0.9,
	}

	// Act
	err := service.validateAndNormalize(parsed)

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, 25000.0, parsed.Amount)
	assert.Equal(t, "Food", parsed.Category)
	assert.Equal(t, "Lunch", parsed.Subcategory)
	assert.Equal(t, "Kopi di Starbucks", parsed.Description)
	assert.Equal(t, "2025-01-15", parsed.Date)
}

func TestValidateAndNormalize_EmptyFields(t *testing.T) {
	// Arrange
	service := &AIService{}
	parsed := &ParsedExpense{
		Amount:     25000,
		Category:   "Food",
		Date:       "2025-01-15",
		Confidence: 0.8,
		// Subcategory and Description are empty
	}

	// Act
	err := service.validateAndNormalize(parsed)

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, "General", parsed.Subcategory)
	assert.Equal(t, "AI parsed expense", parsed.Description)
}

func TestIsValidDateFormat(t *testing.T) {
	tests := []struct {
		name     string
		date     string
		expected bool
	}{
		{"Valid date", "2025-01-15", true},
		{"Invalid format", "15-01-2025", false},
		{"Invalid format 2", "2025/01/15", false},
		{"Invalid date", "2025-13-01", false},
		{"Empty string", "", false},
		{"Invalid day", "2025-01-32", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isValidDateFormat(tt.date)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestConvertToExpense(t *testing.T) {
	// Arrange
	parsed := &ParsedExpense{
		Amount:           25000,
		Category:         "Food",
		Subcategory:      "Lunch",
		Description:      "Kopi di Starbucks",
		Date:             "2025-01-15",
		AdditionalAmount: 3000,
	}

	// Act
	expense := parsed.ConvertToExpense()

	// Assert
	assert.Equal(t, 25000.0, expense.Amount)
	assert.Equal(t, "Food", expense.Category)
	assert.Equal(t, "Lunch", expense.Subcategory)
	assert.Equal(t, "Kopi di Starbucks", expense.Description)
	assert.Equal(t, "2025-01-15", expense.Date)
	assert.Equal(t, 3000.0, expense.AdditionalAmount)
	assert.Equal(t, 0, expense.ID) // Should be 0 for new expense
}

func TestGetSystemPrompt(t *testing.T) {
	// Arrange
	service := &AIService{}

	// Act
	prompt := service.getSystemPrompt()

	// Assert
	assert.Contains(t, prompt, "Indonesian expense descriptions")
	assert.Contains(t, prompt, "Food")
	assert.Contains(t, prompt, "Transportation")
	assert.Contains(t, prompt, "ribu")
	assert.Contains(t, prompt, "confidence")
}

func TestCreatePrompt(t *testing.T) {
	// Arrange
	service := &AIService{}
	text := "Beli kopi 25 ribu"

	// Act
	prompt := service.createPrompt(text)

	// Assert
	assert.Contains(t, prompt, text)
	assert.Contains(t, prompt, "Today's date is:")
	assert.Contains(t, prompt, "JSON object")
}
