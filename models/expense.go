package models

import (
	"database/sql"
	"fmt"
	"time"
)

// DBInterface defines the database operations we need
type DBInterface interface {
	Exec(query string, args ...interface{}) (sql.Result, error)
	Query(query string, args ...interface{}) (*sql.Rows, error)
}

// Expense represents an expense record
type Expense struct {
	ID               int     `json:"id"`
	Amount           float64 `json:"amount"`
	Category         string  `json:"category"`
	Subcategory      string  `json:"subcategory"`
	Description      string  `json:"description,omitempty"`
	Date             string  `json:"date"` // YYYY-MM-DD format
	AdditionalAmount float64 `json:"additional_amount,omitempty"`
	CreatedAt        string  `json:"created_at"`
}

// WeeklySummary represents expense summary for a week
type WeeklySummary struct {
	WeekNumber int     `json:"week_number"` // Week number in the month (1-5)
	StartDate  string  `json:"start_date"`  // YYYY-MM-DD format
	EndDate    string  `json:"end_date"`    // YYYY-MM-DD format
	Total      float64 `json:"total"`
}

// MonthlySummary represents expense summary for a month with weekly breakdown
type MonthlySummary struct {
	Month           int             `json:"month"`
	Year            int             `json:"year"`
	MonthName       string          `json:"month_name"`
	Total           float64         `json:"total"`
	WeeklyBreakdown []WeeklySummary `json:"weekly_breakdown"`
}

// MonthlySummaryItem represents expense summary for a single month in yearly view
type MonthlySummaryItem struct {
	Month     int     `json:"month"`
	MonthName string  `json:"month_name"`
	Total     float64 `json:"total"`
}

// YearlySummary represents expense summary for a year with monthly breakdown
type YearlySummary struct {
	Year             int                  `json:"year"`
	Total            float64              `json:"total"`
	MonthlyBreakdown []MonthlySummaryItem `json:"monthly_breakdown"`
}

// ExpenseRepositoryInterface defines the contract for expense repository operations
type ExpenseRepositoryInterface interface {
	CreateTable() error
	Create(expense *Expense) error
	GetByID(id int) (*Expense, error)
	Update(expense *Expense) error
	Delete(id int) error
	GetAll(filters map[string]string) ([]Expense, error)
	GetMonthlySummary(month, year int) (*MonthlySummary, error)
	GetYearlySummary(year int) (*YearlySummary, error)
}

// ExpenseRepository handles database operations for expenses
type ExpenseRepository struct {
	db DBInterface
}

// NewExpenseRepository creates a new expense repository
func NewExpenseRepository(db *sql.DB) ExpenseRepositoryInterface {
	return &ExpenseRepository{db: db}
}

// CreateTable creates the expenses table if it doesn't exist
func (r *ExpenseRepository) CreateTable() error {
	query := `
	CREATE TABLE IF NOT EXISTS expenses (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		amount REAL NOT NULL,
		category TEXT NOT NULL,
		subcategory TEXT NOT NULL,
		description TEXT,
		date TEXT NOT NULL,
		additional_amount REAL DEFAULT 0,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP
	)`

	_, err := r.db.Exec(query)
	return err
}

// Create inserts a new expense into the database
func (r *ExpenseRepository) Create(expense *Expense) error {
	query := `
	INSERT INTO expenses (amount, category, subcategory, description, date, additional_amount)
	VALUES (?, ?, ?, ?, ?, ?)`

	result, err := r.db.Exec(query,
		expense.Amount,
		expense.Category,
		expense.Subcategory,
		expense.Description,
		expense.Date,
		expense.AdditionalAmount)

	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}

	expense.ID = int(id)
	return nil
}

// GetByID retrieves a single expense by its ID
func (r *ExpenseRepository) GetByID(id int) (*Expense, error) {
	query := "SELECT id, amount, category, subcategory, description, date, additional_amount, created_at FROM expenses WHERE id = ?"

	rows, err := r.db.Query(query, id)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	if !rows.Next() {
		return nil, fmt.Errorf("expense with ID %d not found", id)
	}

	var expense Expense
	var createdAt time.Time

	err = rows.Scan(
		&expense.ID,
		&expense.Amount,
		&expense.Category,
		&expense.Subcategory,
		&expense.Description,
		&expense.Date,
		&expense.AdditionalAmount,
		&createdAt,
	)
	if err != nil {
		return nil, err
	}

	expense.CreatedAt = createdAt.Format("2006-01-02 15:04:05")
	return &expense, nil
}

// Update modifies an existing expense in the database
func (r *ExpenseRepository) Update(expense *Expense) error {
	query := `
	UPDATE expenses
	SET amount = ?, category = ?, subcategory = ?, description = ?, date = ?, additional_amount = ?
	WHERE id = ?`

	result, err := r.db.Exec(query,
		expense.Amount,
		expense.Category,
		expense.Subcategory,
		expense.Description,
		expense.Date,
		expense.AdditionalAmount,
		expense.ID)

	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return fmt.Errorf("expense with ID %d not found", expense.ID)
	}

	return nil
}

// Delete removes an expense from the database
func (r *ExpenseRepository) Delete(id int) error {
	query := "DELETE FROM expenses WHERE id = ?"

	result, err := r.db.Exec(query, id)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return fmt.Errorf("expense with ID %d not found", id)
	}

	return nil
}

// GetAll retrieves all expenses with optional filters
func (r *ExpenseRepository) GetAll(filters map[string]string) ([]Expense, error) {
	query := "SELECT id, amount, category, subcategory, description, date, additional_amount, created_at FROM expenses WHERE 1=1"
	args := []interface{}{}

	// Add filters
	if month, ok := filters["month"]; ok {
		if year, ok := filters["year"]; ok {
			query += " AND strftime('%m', date) = ? AND strftime('%Y', date) = ?"
			args = append(args, fmt.Sprintf("%02s", month), year)
		}
	}

	if category, ok := filters["category"]; ok {
		query += " AND category = ?"
		args = append(args, category)
	}

	if subcategory, ok := filters["subcategory"]; ok {
		query += " AND subcategory = ?"
		args = append(args, subcategory)
	}

	query += " ORDER BY date DESC, created_at DESC"

	rows, err := r.db.Query(query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var expenses []Expense
	for rows.Next() {
		var expense Expense
		var createdAt time.Time

		err := rows.Scan(
			&expense.ID,
			&expense.Amount,
			&expense.Category,
			&expense.Subcategory,
			&expense.Description,
			&expense.Date,
			&expense.AdditionalAmount,
			&createdAt,
		)
		if err != nil {
			return nil, err
		}

		expense.CreatedAt = createdAt.Format("2006-01-02 15:04:05")
		expenses = append(expenses, expense)
	}

	return expenses, nil
}

// GetMonthlySummary retrieves expense summary for a specific month with weekly breakdown
func (r *ExpenseRepository) GetMonthlySummary(month, year int) (*MonthlySummary, error) {
	// Get month name
	monthNames := []string{
		"", "January", "February", "March", "April", "May", "June",
		"July", "August", "September", "October", "November", "December",
	}

	if month < 1 || month > 12 {
		return nil, fmt.Errorf("invalid month: %d", month)
	}

	// Get total for the month
	totalQuery := `
		SELECT COALESCE(SUM(amount + additional_amount), 0) as total
		FROM expenses
		WHERE strftime('%m', date) = ? AND strftime('%Y', date) = ?`

	var total float64
	rows, err := r.db.Query(totalQuery, fmt.Sprintf("%02d", month), fmt.Sprintf("%d", year))
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	if rows.Next() {
		err = rows.Scan(&total)
		if err != nil {
			return nil, err
		}
	}

	// Get weekly breakdown
	weeklyQuery := `
		SELECT
			CAST((strftime('%d', date) - 1) / 7 + 1 AS INTEGER) as week_number,
			MIN(date) as start_date,
			MAX(date) as end_date,
			COALESCE(SUM(amount + additional_amount), 0) as week_total
		FROM expenses
		WHERE strftime('%m', date) = ? AND strftime('%Y', date) = ?
		GROUP BY week_number
		ORDER BY week_number`

	weekRows, err := r.db.Query(weeklyQuery, fmt.Sprintf("%02d", month), fmt.Sprintf("%d", year))
	if err != nil {
		return nil, err
	}
	defer weekRows.Close()

	var weeklyBreakdown []WeeklySummary
	for weekRows.Next() {
		var week WeeklySummary
		err := weekRows.Scan(&week.WeekNumber, &week.StartDate, &week.EndDate, &week.Total)
		if err != nil {
			return nil, err
		}
		weeklyBreakdown = append(weeklyBreakdown, week)
	}

	return &MonthlySummary{
		Month:           month,
		Year:            year,
		MonthName:       monthNames[month],
		Total:           total,
		WeeklyBreakdown: weeklyBreakdown,
	}, nil
}

// GetYearlySummary retrieves expense summary for a specific year with monthly breakdown
func (r *ExpenseRepository) GetYearlySummary(year int) (*YearlySummary, error) {
	// Get month names
	monthNames := []string{
		"", "January", "February", "March", "April", "May", "June",
		"July", "August", "September", "October", "November", "December",
	}

	// Get total for the year
	totalQuery := `
		SELECT COALESCE(SUM(amount + additional_amount), 0) as total
		FROM expenses
		WHERE strftime('%Y', date) = ?`

	var total float64
	rows, err := r.db.Query(totalQuery, fmt.Sprintf("%d", year))
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	if rows.Next() {
		err = rows.Scan(&total)
		if err != nil {
			return nil, err
		}
	}

	// Get monthly breakdown
	monthlyQuery := `
		SELECT
			CAST(strftime('%m', date) AS INTEGER) as month,
			COALESCE(SUM(amount + additional_amount), 0) as month_total
		FROM expenses
		WHERE strftime('%Y', date) = ?
		GROUP BY month
		ORDER BY month`

	monthRows, err := r.db.Query(monthlyQuery, fmt.Sprintf("%d", year))
	if err != nil {
		return nil, err
	}
	defer monthRows.Close()

	var monthlyBreakdown []MonthlySummaryItem
	for monthRows.Next() {
		var item MonthlySummaryItem
		err := monthRows.Scan(&item.Month, &item.Total)
		if err != nil {
			return nil, err
		}
		item.MonthName = monthNames[item.Month]
		monthlyBreakdown = append(monthlyBreakdown, item)
	}

	return &YearlySummary{
		Year:             year,
		Total:            total,
		MonthlyBreakdown: monthlyBreakdown,
	}, nil
}
