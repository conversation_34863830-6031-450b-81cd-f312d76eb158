package handlers

import (
	"errors"
	"go-expense/models"
	serviceMocks "go-expense/services/mocks"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

// GetMonthlySummary Handler Tests

func TestGetMonthlySummary_Success(t *testing.T) {
	// Arrange
	mockService := serviceMocks.NewMockExpenseService(t)
	handler := NewExpenseHandler(mockService)

	expectedSummary := &models.MonthlySummary{
		Month:     1,
		Year:      2025,
		MonthName: "January",
		Total:     150.75,
		WeeklyBreakdown: []models.WeeklySummary{
			{WeekNumber: 1, StartDate: "2025-01-01", EndDate: "2025-01-07", Total: 75.25},
			{WeekNumber: 2, StartDate: "2025-01-08", EndDate: "2025-01-14", Total: 75.50},
		},
	}

	mockService.EXPECT().GetMonthlySummary("1", "2025").Return(expectedSummary, nil).Once()

	// Create HTTP request
	req := httptest.NewRequest("GET", "/expenses/summary/monthly?month=1&year=2025", nil)
	w := httptest.NewRecorder()

	// Act
	handler.GetMonthlySummary(w, req)

	// Assert
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
	assert.Contains(t, w.Body.String(), `"month":1`)
	assert.Contains(t, w.Body.String(), `"year":2025`)
	assert.Contains(t, w.Body.String(), `"month_name":"January"`)
	assert.Contains(t, w.Body.String(), `"total":150.75`)
}

func TestGetMonthlySummary_MethodNotAllowed(t *testing.T) {
	// Arrange
	mockService := serviceMocks.NewMockExpenseService(t)
	handler := NewExpenseHandler(mockService)

	// Create HTTP request with wrong method
	req := httptest.NewRequest("POST", "/expenses/summary/monthly", nil)
	w := httptest.NewRecorder()

	// Act
	handler.GetMonthlySummary(w, req)

	// Assert
	assert.Equal(t, http.StatusMethodNotAllowed, w.Code)
	assert.Contains(t, w.Body.String(), "Method not allowed")
}

func TestGetMonthlySummary_ServiceError(t *testing.T) {
	// Arrange
	mockService := serviceMocks.NewMockExpenseService(t)
	handler := NewExpenseHandler(mockService)
	expectedError := errors.New("month and year parameters are required")

	mockService.EXPECT().GetMonthlySummary("", "").Return(nil, expectedError).Once()

	// Create HTTP request without parameters
	req := httptest.NewRequest("GET", "/expenses/summary/monthly", nil)
	w := httptest.NewRecorder()

	// Act
	handler.GetMonthlySummary(w, req)

	// Assert
	assert.Equal(t, http.StatusBadRequest, w.Code)
	assert.Contains(t, w.Body.String(), "month and year parameters are required")
}

func TestGetMonthlySummary_MissingParameters(t *testing.T) {
	// Arrange
	mockService := serviceMocks.NewMockExpenseService(t)
	handler := NewExpenseHandler(mockService)
	expectedError := errors.New("month and year parameters are required")

	mockService.EXPECT().GetMonthlySummary("", "2025").Return(nil, expectedError).Once()

	// Create HTTP request with only year parameter
	req := httptest.NewRequest("GET", "/expenses/summary/monthly?year=2025", nil)
	w := httptest.NewRecorder()

	// Act
	handler.GetMonthlySummary(w, req)

	// Assert
	assert.Equal(t, http.StatusBadRequest, w.Code)
	assert.Contains(t, w.Body.String(), "month and year parameters are required")
}

// GetYearlySummary Handler Tests

func TestGetYearlySummary_Success(t *testing.T) {
	// Arrange
	mockService := serviceMocks.NewMockExpenseService(t)
	handler := NewExpenseHandler(mockService)

	expectedSummary := &models.YearlySummary{
		Year:  2025,
		Total: 1800.50,
		MonthlyBreakdown: []models.MonthlySummaryItem{
			{Month: 1, MonthName: "January", Total: 150.25},
			{Month: 2, MonthName: "February", Total: 200.75},
			{Month: 3, MonthName: "March", Total: 175.50},
		},
	}

	mockService.EXPECT().GetYearlySummary("2025").Return(expectedSummary, nil).Once()

	// Create HTTP request
	req := httptest.NewRequest("GET", "/expenses/summary/yearly?year=2025", nil)
	w := httptest.NewRecorder()

	// Act
	handler.GetYearlySummary(w, req)

	// Assert
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
	assert.Contains(t, w.Body.String(), `"year":2025`)
	assert.Contains(t, w.Body.String(), `"total":1800.5`)
}

func TestGetYearlySummary_MethodNotAllowed(t *testing.T) {
	// Arrange
	mockService := serviceMocks.NewMockExpenseService(t)
	handler := NewExpenseHandler(mockService)

	// Create HTTP request with wrong method
	req := httptest.NewRequest("POST", "/expenses/summary/yearly", nil)
	w := httptest.NewRecorder()

	// Act
	handler.GetYearlySummary(w, req)

	// Assert
	assert.Equal(t, http.StatusMethodNotAllowed, w.Code)
	assert.Contains(t, w.Body.String(), "Method not allowed")
}

func TestGetYearlySummary_ServiceError(t *testing.T) {
	// Arrange
	mockService := serviceMocks.NewMockExpenseService(t)
	handler := NewExpenseHandler(mockService)
	expectedError := errors.New("year parameter is required")

	mockService.EXPECT().GetYearlySummary("").Return(nil, expectedError).Once()

	// Create HTTP request without parameters
	req := httptest.NewRequest("GET", "/expenses/summary/yearly", nil)
	w := httptest.NewRecorder()

	// Act
	handler.GetYearlySummary(w, req)

	// Assert
	assert.Equal(t, http.StatusBadRequest, w.Code)
	assert.Contains(t, w.Body.String(), "year parameter is required")
}

func TestGetYearlySummary_InvalidYear(t *testing.T) {
	// Arrange
	mockService := serviceMocks.NewMockExpenseService(t)
	handler := NewExpenseHandler(mockService)
	expectedError := errors.New("invalid year parameter: must be between 1900 and 2100")

	mockService.EXPECT().GetYearlySummary("1800").Return(nil, expectedError).Once()

	// Create HTTP request with invalid year
	req := httptest.NewRequest("GET", "/expenses/summary/yearly?year=1800", nil)
	w := httptest.NewRecorder()

	// Act
	handler.GetYearlySummary(w, req)

	// Assert
	assert.Equal(t, http.StatusBadRequest, w.Code)
	assert.Contains(t, w.Body.String(), "invalid year parameter")
}
