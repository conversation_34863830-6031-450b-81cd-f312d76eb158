package utils

import (
	"go-expense/models"
	"testing"

	"github.com/stretchr/testify/assert"
)

// Test fixtures

func createTestExpenseList() []models.Expense {
	return []models.Expense{
		{
			ID:               1,
			Amount:           25.50,
			Category:         "Food",
			Subcategory:      "Lunch",
			Description:      "Pizza lunch",
			Date:             "2025-01-15",
			AdditionalAmount: 3.00,
			CreatedAt:        "2025-01-15 12:00:00",
		},
		{
			ID:               2,
			Amount:           45.00,
			Category:         "Transportation",
			Subcategory:      "Gas",
			Description:      "Gas station",
			Date:             "2025-01-20",
			AdditionalAmount: 0,
			CreatedAt:        "2025-01-20 10:30:00",
		},
		{
			ID:               3,
			Amount:           12.99,
			Category:         "Food",
			Subcategory:      "Coffee",
			Description:      "",
			Date:             "2025-01-22",
			AdditionalAmount: 1.50,
			CreatedAt:        "2025-01-22 08:15:00",
		},
	}
}

func createEmptyExpenseList() []models.Expense {
	return []models.Expense{}
}

func createSingleExpenseList() []models.Expense {
	return []models.Expense{
		{
			ID:               1,
			Amount:           100.00,
			Category:         "Test",
			Subcategory:      "Single",
			Description:      "Single expense test",
			Date:             "2025-01-01",
			AdditionalAmount: 5.00,
			CreatedAt:        "2025-01-01 00:00:00",
		},
	}
}

// ExportToExcel Tests

func TestExportToExcel_ValidExpenseList(t *testing.T) {
	// Arrange
	expenses := createTestExpenseList()
	month := "1"
	year := "2025"

	// Act
	excelFile, err := ExportToExcel(expenses, month, year)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, excelFile)

	// Verify sheet exists
	sheetName := "Expenses_1_2025"
	sheetIndex, err := excelFile.GetSheetIndex(sheetName)
	assert.NoError(t, err)
	assert.NotEqual(t, -1, sheetIndex)

	// Verify headers
	headers := []string{"ID", "Amount", "Category", "Subcategory", "Description", "Date", "Additional Amount", "Total", "Created At"}
	for i, header := range headers {
		cell := string(rune('A'+i)) + "1"
		value, err := excelFile.GetCellValue(sheetName, cell)
		assert.NoError(t, err)
		assert.Equal(t, header, value)
	}

	// Verify first expense data
	firstExpenseRow := "2"
	idValue, err := excelFile.GetCellValue(sheetName, "A"+firstExpenseRow)
	assert.NoError(t, err)
	assert.Equal(t, "1", idValue)

	amountValue, err := excelFile.GetCellValue(sheetName, "B"+firstExpenseRow)
	assert.NoError(t, err)
	assert.Equal(t, "25.5", amountValue)

	categoryValue, err := excelFile.GetCellValue(sheetName, "C"+firstExpenseRow)
	assert.NoError(t, err)
	assert.Equal(t, "Food", categoryValue)

	// Verify total calculation (amount + additional_amount)
	totalValue, err := excelFile.GetCellValue(sheetName, "H"+firstExpenseRow)
	assert.NoError(t, err)
	assert.Equal(t, "28.5", totalValue) // 25.50 + 3.00

	excelFile.Close()
}

func TestExportToExcel_EmptyExpenseList(t *testing.T) {
	// Arrange
	expenses := createEmptyExpenseList()
	month := "2"
	year := "2025"

	// Act
	excelFile, err := ExportToExcel(expenses, month, year)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, excelFile)

	// Verify sheet exists
	sheetName := "Expenses_2_2025"
	sheetIndex, err := excelFile.GetSheetIndex(sheetName)
	assert.NoError(t, err)
	assert.NotEqual(t, -1, sheetIndex)

	// Verify headers still exist
	idHeader, err := excelFile.GetCellValue(sheetName, "A1")
	assert.NoError(t, err)
	assert.Equal(t, "ID", idHeader)

	// Verify no data rows (only headers)
	secondRowValue, err := excelFile.GetCellValue(sheetName, "A2")
	assert.NoError(t, err)
	assert.Empty(t, secondRowValue)

	excelFile.Close()
}

func TestExportToExcel_SingleExpense(t *testing.T) {
	// Arrange
	expenses := createSingleExpenseList()
	month := "12"
	year := "2024"

	// Act
	excelFile, err := ExportToExcel(expenses, month, year)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, excelFile)

	// Verify sheet name
	sheetName := "Expenses_12_2024"
	sheetIndex, err := excelFile.GetSheetIndex(sheetName)
	assert.NoError(t, err)
	assert.NotEqual(t, -1, sheetIndex)

	// Verify expense data
	amountValue, err := excelFile.GetCellValue(sheetName, "B2")
	assert.NoError(t, err)
	assert.Equal(t, "100", amountValue)

	totalValue, err := excelFile.GetCellValue(sheetName, "H2")
	assert.NoError(t, err)
	assert.Equal(t, "105", totalValue) // 100.00 + 5.00

	// Verify summary row exists
	summaryRowLabel, err := excelFile.GetCellValue(sheetName, "A4") // Row 4 for single expense
	assert.NoError(t, err)
	assert.Equal(t, "TOTAL:", summaryRowLabel)

	summaryAmount, err := excelFile.GetCellValue(sheetName, "B4")
	assert.NoError(t, err)
	assert.Equal(t, "100", summaryAmount)

	summaryTotal, err := excelFile.GetCellValue(sheetName, "H4")
	assert.NoError(t, err)
	assert.Equal(t, "105", summaryTotal)

	excelFile.Close()
}

func TestExportToExcel_MultipleExpenses_SummaryCalculation(t *testing.T) {
	// Arrange
	expenses := createTestExpenseList()
	month := "1"
	year := "2025"

	// Act
	excelFile, err := ExportToExcel(expenses, month, year)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, excelFile)

	sheetName := "Expenses_1_2025"

	// Verify summary row (should be at row 6: 3 expenses + 1 header + 1 empty + 1 summary)
	summaryRow := "6"
	summaryLabel, err := excelFile.GetCellValue(sheetName, "A"+summaryRow)
	assert.NoError(t, err)
	assert.Equal(t, "TOTAL:", summaryLabel)

	// Calculate expected totals
	expectedTotalAmount := 25.50 + 45.00 + 12.99       // 83.49
	expectedAdditionalAmount := 3.00 + 0 + 1.50        // 4.50
	_ = expectedTotalAmount + expectedAdditionalAmount // 87.99 (for reference)

	summaryAmount, err := excelFile.GetCellValue(sheetName, "B"+summaryRow)
	assert.NoError(t, err)
	assert.Equal(t, "83.49", summaryAmount)

	summaryAdditional, err := excelFile.GetCellValue(sheetName, "G"+summaryRow)
	assert.NoError(t, err)
	assert.Equal(t, "4.5", summaryAdditional)

	summaryGrandTotal, err := excelFile.GetCellValue(sheetName, "H"+summaryRow)
	assert.NoError(t, err)
	assert.Equal(t, "87.99", summaryGrandTotal)

	excelFile.Close()
}

func TestExportToExcel_ExpenseWithEmptyDescription(t *testing.T) {
	// Arrange
	expenses := []models.Expense{
		{
			ID:               1,
			Amount:           50.00,
			Category:         "Test",
			Subcategory:      "Empty",
			Description:      "", // Empty description
			Date:             "2025-01-01",
			AdditionalAmount: 0,
			CreatedAt:        "2025-01-01 00:00:00",
		},
	}
	month := "1"
	year := "2025"

	// Act
	excelFile, err := ExportToExcel(expenses, month, year)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, excelFile)

	sheetName := "Expenses_1_2025"
	descriptionValue, err := excelFile.GetCellValue(sheetName, "E2")
	assert.NoError(t, err)
	assert.Empty(t, descriptionValue)

	excelFile.Close()
}

func TestExportToExcel_ExpenseWithZeroAdditionalAmount(t *testing.T) {
	// Arrange
	expenses := []models.Expense{
		{
			ID:               1,
			Amount:           25.00,
			Category:         "Test",
			Subcategory:      "Zero",
			Description:      "Zero additional",
			Date:             "2025-01-01",
			AdditionalAmount: 0, // Zero additional amount
			CreatedAt:        "2025-01-01 00:00:00",
		},
	}
	month := "1"
	year := "2025"

	// Act
	excelFile, err := ExportToExcel(expenses, month, year)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, excelFile)

	sheetName := "Expenses_1_2025"
	additionalValue, err := excelFile.GetCellValue(sheetName, "G2")
	assert.NoError(t, err)
	assert.Equal(t, "0", additionalValue)

	totalValue, err := excelFile.GetCellValue(sheetName, "H2")
	assert.NoError(t, err)
	assert.Equal(t, "25", totalValue) // Should equal amount when additional is 0

	excelFile.Close()
}

// ValidateMonthYear Tests

func TestValidateMonthYear_ValidValues(t *testing.T) {
	testCases := []struct {
		name     string
		monthStr string
		yearStr  string
		expMonth int
		expYear  int
	}{
		{"January2025", "1", "2025", 1, 2025},
		{"December2024", "12", "2024", 12, 2024},
		{"June2000", "6", "2000", 6, 2000},
		{"February2100", "2", "2100", 2, 2100},
		{"MinYear", "1", "1900", 1, 1900},
		{"MaxYear", "12", "2100", 12, 2100},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Act
			month, year, err := ValidateMonthYear(tc.monthStr, tc.yearStr)

			// Assert
			assert.NoError(t, err)
			assert.Equal(t, tc.expMonth, month)
			assert.Equal(t, tc.expYear, year)
		})
	}
}

func TestValidateMonthYear_InvalidMonth(t *testing.T) {
	testCases := []struct {
		name     string
		monthStr string
		yearStr  string
	}{
		{"MonthZero", "0", "2025"},
		{"MonthThirteen", "13", "2025"},
		{"MonthNegative", "-1", "2025"},
		{"MonthNonNumeric", "abc", "2025"},
		{"MonthEmpty", "", "2025"},
		{"MonthFloat", "1.5", "2025"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Act
			month, year, err := ValidateMonthYear(tc.monthStr, tc.yearStr)

			// Assert
			assert.Error(t, err)
			assert.Equal(t, 0, month)
			assert.Equal(t, 0, year)
			assert.Contains(t, err.Error(), "invalid month")
		})
	}
}

func TestValidateMonthYear_InvalidYear(t *testing.T) {
	testCases := []struct {
		name     string
		monthStr string
		yearStr  string
	}{
		{"YearTooLow", "1", "1899"},
		{"YearTooHigh", "1", "2101"},
		{"YearNegative", "1", "-2025"},
		{"YearNonNumeric", "1", "abc"},
		{"YearEmpty", "1", ""},
		{"YearFloat", "1", "2025.5"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Act
			month, year, err := ValidateMonthYear(tc.monthStr, tc.yearStr)

			// Assert
			assert.Error(t, err)
			assert.Equal(t, 0, month)
			assert.Equal(t, 0, year)
			assert.Contains(t, err.Error(), "invalid year")
		})
	}
}

func TestValidateMonthYear_BothInvalid(t *testing.T) {
	// Act
	month, year, err := ValidateMonthYear("13", "1800")

	// Assert
	assert.Error(t, err)
	assert.Equal(t, 0, month)
	assert.Equal(t, 0, year)
	// Should return month error first (as it's checked first in the function)
	assert.Contains(t, err.Error(), "invalid month")
}
