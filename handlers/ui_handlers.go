package handlers

import (
	"fmt"
	"go-expense/models"
	"go-expense/services"
	"html/template"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"
)

// formatCurrency formats a float64 amount as Indonesian Rupiah with dot separators
func formatCurrency(amount float64) string {
	// Convert to string with 2 decimal places
	str := fmt.Sprintf("%.0f", amount)

	// Add thousand separators (dots)
	if len(str) <= 3 {
		return "Rp " + str
	}

	// Add dots every 3 digits from the right
	result := ""
	for i, digit := range str {
		if i > 0 && (len(str)-i)%3 == 0 {
			result += "."
		}
		result += string(digit)
	}

	return "Rp " + result
}

type UIHandler struct {
	expenseService services.ExpenseServiceInterface
	templates      *template.Template
}

type PageData struct {
	Title      string
	ActivePage string
}

type DashboardData struct {
	PageData
	TotalExpenses     float64
	ExpenseCount      int
	AverageExpense    float64
	TopCategory       string
	RecentExpenses    []models.Expense
	CategoryBreakdown []CategorySummary
}

type ExpensesData struct {
	PageData
	Expenses      []models.Expense
	Categories    []string
	Subcategories []string
	Filters       ExpenseFilters
	Summary       *ExpenseSummary
}

type AddExpenseData struct {
	PageData
	Success    bool
	Error      string
	FormData   ExpenseFormData
	IsEditMode bool
}

type EditExpenseData struct {
	PageData
	Success    bool
	Error      string
	FormData   ExpenseFormData
	ExpenseID  string
	IsEditMode bool
}

type SummaryData struct {
	PageData
	Summary   *DetailedSummary
	Filters   SummaryFilters
	MonthName string
}

type ExpenseFilters struct {
	Month       string
	Year        string
	Category    string
	Subcategory string
}

type SummaryFilters struct {
	Month string
	Year  string
}

type ExpenseFormData struct {
	Amount           string
	Category         string
	Subcategory      string
	Description      string
	Date             string
	AdditionalAmount string
}

type ExpenseSummary struct {
	Total   float64
	Count   int
	Average float64
}

type CategorySummary struct {
	Category       string
	Amount         float64
	Percentage     float64
	TopSubcategory string
	TopSubAmount   float64
}

type DetailedSummary struct {
	TotalExpense          float64
	TotalAmount           float64
	TotalAdditionalAmount float64
	ExpensesCount         int
	AverageAmount         float64
	HighestAmount         float64
	CategoryBreakdown     []CategoryBreakdown
	WeeklyBreakdown       []WeeklyBreakdown
	MonthlyBreakdown      []MonthlyBreakdown
}

type CategoryBreakdown struct {
	Category      string
	Amount        float64
	Count         int
	Average       float64
	Percentage    float64
	Subcategories []SubcategoryBreakdown
}

type SubcategoryBreakdown struct {
	Subcategory string
	Amount      float64
	Count       int
	Average     float64
	Percentage  float64 // Percentage of total expenses (not just category)
}

type WeeklyBreakdown struct {
	Week    int
	Amount  float64
	Count   int
	Average float64
}

type MonthlyBreakdown struct {
	Month     int
	MonthName string
	Amount    float64
	Count     int
	Average   float64
}

func NewUIHandler(expenseService services.ExpenseServiceInterface) *UIHandler {
	// Create template functions
	funcMap := template.FuncMap{
		"add": func(a, b float64) float64 {
			return a + b
		},
		"mul": func(a, b float64) float64 {
			return a * b
		},
		"formatCurrency": func(amount float64) string {
			return formatCurrency(amount)
		},
	}

	// Parse templates
	templates := template.Must(template.New("").Funcs(funcMap).ParseGlob("templates/*.html"))

	return &UIHandler{
		expenseService: expenseService,
		templates:      templates,
	}
}

func (h *UIHandler) Dashboard(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get current month/year for dashboard stats
	now := time.Now()
	month := int(now.Month())
	year := now.Year()

	// Get expenses for current month
	filters := map[string]string{
		"month": strconv.Itoa(month),
		"year":  strconv.Itoa(year),
	}
	expenses, err := h.expenseService.GetExpenses(filters)
	if err != nil {
		http.Error(w, "Failed to fetch expenses", http.StatusInternalServerError)
		return
	}

	// Calculate dashboard stats
	totalExpenses := 0.0
	categoryTotals := make(map[string]float64)
	subcategoryTotals := make(map[string]map[string]float64)

	for _, expense := range expenses {
		total := expense.Amount + expense.AdditionalAmount
		totalExpenses += total
		categoryTotals[expense.Category] += total

		// Track subcategories for each category
		if subcategoryTotals[expense.Category] == nil {
			subcategoryTotals[expense.Category] = make(map[string]float64)
		}
		subcategoryTotals[expense.Category][expense.Subcategory] += total
	}

	averageExpense := 0.0
	if len(expenses) > 0 {
		averageExpense = totalExpenses / float64(len(expenses))
	}

	// Find top category
	topCategory := "None"
	maxAmount := 0.0
	for category, amount := range categoryTotals {
		if amount > maxAmount {
			maxAmount = amount
			topCategory = category
		}
	}

	// Get recent expenses (last 5)
	recentExpenses := expenses
	if len(recentExpenses) > 5 {
		recentExpenses = recentExpenses[:5]
	}

	// Calculate category breakdown with top subcategory
	var categoryBreakdown []CategorySummary
	for category, amount := range categoryTotals {
		percentage := 0.0
		if totalExpenses > 0 {
			percentage = (amount / totalExpenses) * 100
		}

		// Find top subcategory for this category
		topSubcategory := ""
		topSubAmount := 0.0
		if subcategoryTotals[category] != nil {
			for subcategory, subAmount := range subcategoryTotals[category] {
				if subAmount > topSubAmount {
					topSubAmount = subAmount
					topSubcategory = subcategory
				}
			}
		}

		categoryBreakdown = append(categoryBreakdown, CategorySummary{
			Category:       category,
			Amount:         amount,
			Percentage:     percentage,
			TopSubcategory: topSubcategory,
			TopSubAmount:   topSubAmount,
		})
	}

	// Sort categories alphabetically
	sort.Slice(categoryBreakdown, func(i, j int) bool {
		return categoryBreakdown[i].Category < categoryBreakdown[j].Category
	})

	data := DashboardData{
		PageData: PageData{
			Title:      "Dashboard",
			ActivePage: "dashboard",
		},
		TotalExpenses:     totalExpenses,
		ExpenseCount:      len(expenses),
		AverageExpense:    averageExpense,
		TopCategory:       topCategory,
		RecentExpenses:    recentExpenses,
		CategoryBreakdown: categoryBreakdown,
	}

	if err := h.templates.ExecuteTemplate(w, "base.html", data); err != nil {
		http.Error(w, "Failed to render template", http.StatusInternalServerError)
		return
	}
}

func (h *UIHandler) Expenses(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Parse filters
	filters := ExpenseFilters{
		Month:       r.URL.Query().Get("month"),
		Year:        r.URL.Query().Get("year"),
		Category:    r.URL.Query().Get("category"),
		Subcategory: r.URL.Query().Get("subcategory"),
	}

	// Convert filters to service parameters
	month := 0
	year := 0
	if filters.Month != "" {
		month, _ = strconv.Atoi(filters.Month)
	}
	if filters.Year != "" {
		year, _ = strconv.Atoi(filters.Year)
	}

	// Build filters map
	filterMap := make(map[string]string)
	if month > 0 {
		filterMap["month"] = strconv.Itoa(month)
	}
	if year > 0 {
		filterMap["year"] = strconv.Itoa(year)
	}
	if filters.Category != "" {
		filterMap["category"] = filters.Category
	}
	if filters.Subcategory != "" {
		filterMap["subcategory"] = filters.Subcategory
	}

	// Get expenses
	expenses, err := h.expenseService.GetExpenses(filterMap)
	if err != nil {
		http.Error(w, "Failed to fetch expenses", http.StatusInternalServerError)
		return
	}

	// Get all categories and subcategories for filter dropdowns
	allExpenses, _ := h.expenseService.GetExpenses(map[string]string{})
	categories := make(map[string]bool)
	subcategories := make(map[string]bool)

	for _, expense := range allExpenses {
		categories[expense.Category] = true
		if expense.Subcategory != "" {
			subcategories[expense.Subcategory] = true
		}
	}

	var categoryList []string
	var subcategoryList []string
	for category := range categories {
		categoryList = append(categoryList, category)
	}
	for subcategory := range subcategories {
		subcategoryList = append(subcategoryList, subcategory)
	}

	// Calculate summary
	var summary *ExpenseSummary
	if len(expenses) > 0 {
		total := 0.0
		for _, expense := range expenses {
			total += expense.Amount + expense.AdditionalAmount
		}
		summary = &ExpenseSummary{
			Total:   total,
			Count:   len(expenses),
			Average: total / float64(len(expenses)),
		}
	}

	data := ExpensesData{
		PageData: PageData{
			Title:      "Expenses",
			ActivePage: "expenses",
		},
		Expenses:      expenses,
		Categories:    categoryList,
		Subcategories: subcategoryList,
		Filters:       filters,
		Summary:       summary,
	}

	if err := h.templates.ExecuteTemplate(w, "base.html", data); err != nil {
		http.Error(w, "Failed to render template", http.StatusInternalServerError)
		return
	}
}

func (h *UIHandler) AddExpense(w http.ResponseWriter, r *http.Request) {
	data := AddExpenseData{
		PageData: PageData{
			Title:      "Add Expense",
			ActivePage: "add",
		},
		IsEditMode: false,
	}

	if r.Method == http.MethodPost {
		// Parse form data
		if err := r.ParseForm(); err != nil {
			data.Error = "Failed to parse form data"
		} else {
			// Get form values
			amountStr := r.FormValue("amount")
			category := r.FormValue("category")
			subcategory := r.FormValue("subcategory")
			description := r.FormValue("description")
			dateStr := r.FormValue("date")
			additionalAmountStr := r.FormValue("additional_amount")

			// Store form data for redisplay
			data.FormData = ExpenseFormData{
				Amount:           amountStr,
				Category:         category,
				Subcategory:      subcategory,
				Description:      description,
				Date:             dateStr,
				AdditionalAmount: additionalAmountStr,
			}

			// Validate and convert data
			amount, err := strconv.ParseFloat(amountStr, 64)
			if err != nil || amount <= 0 {
				data.Error = "Please enter a valid amount"
			} else if category == "" {
				data.Error = "Please select a category"
			} else if description == "" {
				data.Error = "Please enter a description"
			} else if dateStr == "" {
				data.Error = "Please select a date"
			} else {
				// Parse additional amount
				additionalAmount := 0.0
				if additionalAmountStr != "" {
					additionalAmount, _ = strconv.ParseFloat(additionalAmountStr, 64)
				}

				// Create expense
				expense := models.Expense{
					Amount:           amount,
					Category:         category,
					Subcategory:      subcategory,
					Description:      description,
					Date:             dateStr,
					AdditionalAmount: additionalAmount,
				}

				// Save expense
				if err := h.expenseService.CreateExpense(&expense); err != nil {
					data.Error = "Failed to save expense: " + err.Error()
				} else {
					data.Success = true
					// Clear form data on success
					data.FormData = ExpenseFormData{}
				}
			}
		}
	}

	// Check if this is an HTMX request
	if r.Header.Get("HX-Request") == "true" {
		// For HTMX requests, return only the notification partial
		if err := h.templates.ExecuteTemplate(w, "notification-partial", data); err != nil {
			http.Error(w, "Failed to render template", http.StatusInternalServerError)
			return
		}
	} else {
		// For regular requests, return the full page
		if err := h.templates.ExecuteTemplate(w, "base.html", data); err != nil {
			http.Error(w, "Failed to render template", http.StatusInternalServerError)
			return
		}
	}
}

func (h *UIHandler) EditExpense(w http.ResponseWriter, r *http.Request) {
	// Extract expense ID from URL path
	path := r.URL.Path
	if !strings.HasPrefix(path, "/ui/edit/") {
		http.Error(w, "Invalid URL", http.StatusBadRequest)
		return
	}

	expenseID := strings.TrimPrefix(path, "/ui/edit/")
	if expenseID == "" {
		http.Error(w, "Expense ID is required", http.StatusBadRequest)
		return
	}

	data := EditExpenseData{
		PageData: PageData{
			Title:      "Edit Expense",
			ActivePage: "edit",
		},
		ExpenseID:  expenseID,
		IsEditMode: true,
	}

	if r.Method == http.MethodGet {
		// Load existing expense data
		expense, err := h.expenseService.GetExpenseByID(expenseID)
		if err != nil {
			http.Error(w, "Expense not found", http.StatusNotFound)
			return
		}

		// Populate form data with existing expense
		data.FormData = ExpenseFormData{
			Amount:           fmt.Sprintf("%.2f", expense.Amount),
			Category:         expense.Category,
			Subcategory:      expense.Subcategory,
			Description:      expense.Description,
			Date:             expense.Date,
			AdditionalAmount: fmt.Sprintf("%.2f", expense.AdditionalAmount),
		}

	} else if r.Method == http.MethodPost {
		// Parse form data
		if err := r.ParseForm(); err != nil {
			data.Error = "Failed to parse form data"
		} else {
			// Get form values
			amountStr := r.FormValue("amount")
			category := r.FormValue("category")
			subcategory := r.FormValue("subcategory")
			description := r.FormValue("description")
			dateStr := r.FormValue("date")
			additionalAmountStr := r.FormValue("additional_amount")

			// Store form data for redisplay
			data.FormData = ExpenseFormData{
				Amount:           amountStr,
				Category:         category,
				Subcategory:      subcategory,
				Description:      description,
				Date:             dateStr,
				AdditionalAmount: additionalAmountStr,
			}

			// Validate and convert data
			amount, err := strconv.ParseFloat(amountStr, 64)
			if err != nil || amount <= 0 {
				data.Error = "Please enter a valid amount"
			} else if category == "" {
				data.Error = "Please select a category"
			} else if description == "" {
				data.Error = "Please enter a description"
			} else if dateStr == "" {
				data.Error = "Please select a date"
			} else {
				// Parse additional amount
				additionalAmount := 0.0
				if additionalAmountStr != "" {
					additionalAmount, _ = strconv.ParseFloat(additionalAmountStr, 64)
				}

				// Convert expense ID to int
				id, err := strconv.Atoi(expenseID)
				if err != nil {
					data.Error = "Invalid expense ID"
				} else {
					// Create expense object for update
					expense := models.Expense{
						ID:               id,
						Amount:           amount,
						Category:         category,
						Subcategory:      subcategory,
						Description:      description,
						Date:             dateStr,
						AdditionalAmount: additionalAmount,
					}

					// Update expense
					if err := h.expenseService.UpdateExpense(&expense); err != nil {
						data.Error = "Failed to update expense: " + err.Error()
					} else {
						data.Success = true
					}
				}
			}
		}
	} else {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Check if this is an HTMX request
	if r.Header.Get("HX-Request") == "true" {
		// For HTMX requests, return only the notification partial
		if err := h.templates.ExecuteTemplate(w, "notification-partial", data); err != nil {
			http.Error(w, "Failed to render template", http.StatusInternalServerError)
			return
		}
	} else {
		// For regular requests, return the full page
		if err := h.templates.ExecuteTemplate(w, "base.html", data); err != nil {
			http.Error(w, "Failed to render template", http.StatusInternalServerError)
			return
		}
	}
}

func (h *UIHandler) Summary(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Parse filters
	filters := SummaryFilters{
		Month: r.URL.Query().Get("month"),
		Year:  r.URL.Query().Get("year"),
	}

	data := SummaryData{
		PageData: PageData{
			Title:      "Summary",
			ActivePage: "summary",
		},
		Filters: filters,
	}

	// If year is provided, generate summary
	if filters.Year != "" {
		year, err := strconv.Atoi(filters.Year)
		if err != nil {
			http.Error(w, "Invalid year", http.StatusBadRequest)
			return
		}

		month := 0
		if filters.Month != "" {
			month, err = strconv.Atoi(filters.Month)
			if err != nil {
				http.Error(w, "Invalid month", http.StatusBadRequest)
				return
			}
			// Set month name for display
			monthNames := []string{"", "January", "February", "March", "April", "May", "June",
				"July", "August", "September", "October", "November", "December"}
			if month >= 1 && month <= 12 {
				data.MonthName = monthNames[month]
			}
		}

		// Get summary from API
		var summaryURL string
		if month > 0 {
			summaryURL = fmt.Sprintf("/expenses/summary/monthly?month=%d&year=%d", month, year)
		} else {
			summaryURL = fmt.Sprintf("/expenses/summary/yearly?year=%d", year)
		}

		// Create a new request to get summary data
		req, _ := http.NewRequest("GET", summaryURL, nil)
		req.URL.Scheme = "http"
		req.URL.Host = r.Host

		// We'll call the existing API handler directly
		// This is a bit of a hack, but it reuses existing logic
		summary, err := h.getSummaryData(month, year)
		if err != nil {
			http.Error(w, "Failed to get summary data", http.StatusInternalServerError)
			return
		}

		data.Summary = summary
	}

	if err := h.templates.ExecuteTemplate(w, "base.html", data); err != nil {
		http.Error(w, "Failed to render template", http.StatusInternalServerError)
		return
	}
}

func (h *UIHandler) getSummaryData(month, year int) (*DetailedSummary, error) {
	// Build filters map
	filterMap := make(map[string]string)
	if month > 0 {
		filterMap["month"] = strconv.Itoa(month)
	}
	if year > 0 {
		filterMap["year"] = strconv.Itoa(year)
	}

	// Get expenses for the period
	expenses, err := h.expenseService.GetExpenses(filterMap)
	if err != nil {
		return nil, err
	}

	if len(expenses) == 0 {
		return &DetailedSummary{}, nil
	}

	// Calculate totals
	totalExpenses := 0.0
	totalAmount := 0.0
	totalAdditionalAmount := 0.0
	highestAmount := 0.0
	categoryTotals := make(map[string]float64)
	categoryCounts := make(map[string]int)
	subcategoryTotals := make(map[string]map[string]float64)
	subcategoryCounts := make(map[string]map[string]int)

	for _, expense := range expenses {
		total := expense.Amount + expense.AdditionalAmount
		totalAmount += expense.Amount
		totalAdditionalAmount += expense.AdditionalAmount
		totalExpenses += total
		if total > highestAmount {
			highestAmount = total
		}

		categoryTotals[expense.Category] += total
		categoryCounts[expense.Category]++

		// Initialize subcategory maps if needed
		if subcategoryTotals[expense.Category] == nil {
			subcategoryTotals[expense.Category] = make(map[string]float64)
			subcategoryCounts[expense.Category] = make(map[string]int)
		}
		subcategoryTotals[expense.Category][expense.Subcategory] += total
		subcategoryCounts[expense.Category][expense.Subcategory]++
	}

	averageAmount := totalExpenses / float64(len(expenses))

	// Build category breakdown with subcategories
	var categoryBreakdown []CategoryBreakdown
	for category, amount := range categoryTotals {
		count := categoryCounts[category]
		percentage := (amount / totalExpenses) * 100

		// Build subcategory breakdown for this category
		var subcategories []SubcategoryBreakdown
		if subcategoryTotals[category] != nil {
			for subcategory, subAmount := range subcategoryTotals[category] {
				subCount := subcategoryCounts[category][subcategory]
				subPercentage := (subAmount / totalExpenses) * 100
				subcategories = append(subcategories, SubcategoryBreakdown{
					Subcategory: subcategory,
					Amount:      subAmount,
					Count:       subCount,
					Average:     subAmount / float64(subCount),
					Percentage:  subPercentage,
				})
			}
		}

		categoryBreakdown = append(categoryBreakdown, CategoryBreakdown{
			Category:      category,
			Amount:        amount,
			Count:         count,
			Average:       amount / float64(count),
			Percentage:    percentage,
			Subcategories: subcategories,
		})
	}

	// Sort categories alphabetically
	sort.Slice(categoryBreakdown, func(i, j int) bool {
		return categoryBreakdown[i].Category < categoryBreakdown[j].Category
	})

	// Sort subcategories within each category alphabetically
	for i := range categoryBreakdown {
		sort.Slice(categoryBreakdown[i].Subcategories, func(a, b int) bool {
			return categoryBreakdown[i].Subcategories[a].Subcategory < categoryBreakdown[i].Subcategories[b].Subcategory
		})
	}

	// Build monthly breakdown if no specific month is selected
	var monthlyBreakdown []MonthlyBreakdown
	if month == 0 {
		monthTotals := make(map[int]float64)
		monthCounts := make(map[int]int)

		for _, expense := range expenses {
			expenseDate, err := time.Parse("2006-01-02", expense.Date)
			if err != nil {
				continue // Skip invalid dates
			}
			expenseMonth := int(expenseDate.Month())
			total := expense.Amount + expense.AdditionalAmount
			monthTotals[expenseMonth] += total
			monthCounts[expenseMonth]++
		}

		// Month names
		monthNames := []string{
			"", "January", "February", "March", "April", "May", "June",
			"July", "August", "September", "October", "November", "December",
		}

		// Build monthly breakdown array
		for month := 1; month <= 12; month++ {
			if monthTotals[month] > 0 {
				monthlyBreakdown = append(monthlyBreakdown, MonthlyBreakdown{
					Month:     month,
					MonthName: monthNames[month],
					Amount:    monthTotals[month],
					Count:     monthCounts[month],
					Average:   monthTotals[month] / float64(monthCounts[month]),
				})
			}
		}

		// Sort by month (ascending order as per user preference)
		sort.Slice(monthlyBreakdown, func(i, j int) bool {
			return monthlyBreakdown[i].Month < monthlyBreakdown[j].Month
		})
	}

	summary := &DetailedSummary{
		TotalExpense:          totalExpenses,
		TotalAmount:           totalAmount,
		TotalAdditionalAmount: totalAdditionalAmount,
		ExpensesCount:         len(expenses),
		AverageAmount:         averageAmount,
		CategoryBreakdown:     categoryBreakdown,
		MonthlyBreakdown:      monthlyBreakdown,
	}

	return summary, nil
}
