package main

import (
	"flag"
	"fmt"
	"go-expense/database"
	"go-expense/utils"
	"log"
)

func main() {
	csvPath := flag.String("file", "./export/sample2.csv", "Path to CSV file")
	dbPath := flag.String("db", "./data/expenses.db", "Path to SQLite database")
	flag.Parse()

	if *csvPath == "" {
		log.Fatal("CSV file path is required")
	}

	db, err := database.InitDB(*dbPath)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	count, err := utils.BulkImportCSV(*csvPath, db)
	if err != nil {
		log.Fatal("Import failed:", err)
	}

	fmt.Printf("Successfully imported %d expenses\n", count)
}
