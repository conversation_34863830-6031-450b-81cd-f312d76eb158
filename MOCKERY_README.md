# Mockery-Generated Mocks

This document describes the Mockery-generated mock implementations for the Go Expense Tracker application.

## Overview

We use [Mo<PERSON>y](https://github.com/vektra/mockery) to automatically generate mocks from interfaces. These mocks provide powerful features like:

- **Call tracking and verification**
- **Argument matching and validation**
- **Return value configuration**
- **Side effect simulation with Run functions**
- **Automatic expectation assertion**

## Generated Mocks

### Repository Mock
- **File**: `models/mocks/MockExpenseRepository.go`
- **Interface**: `models.ExpenseRepositoryInterface`
- **Constructor**: `NewMockExpenseRepository(t)`

### Service Mock
- **File**: `services/mocks/MockExpenseService.go`
- **Interface**: `services.ExpenseServiceInterface`
- **Constructor**: `NewMockExpenseService(t)`

## Configuration

**File**: `.mockery.yaml`

```yaml
with-expecter: true
dir: "{{.InterfaceDir}}/mocks"
outpkg: mocks
packages:
  go-expense/models:
    interfaces:
      ExpenseRepositoryInterface:
        config:
          mockname: MockExpenseRepository
          filename: MockExpenseRepository.go
  go-expense/services:
    interfaces:
      ExpenseServiceInterface:
        config:
          mockname: MockExpenseService
          filename: MockExpenseService.go
```

## Regenerating Mocks

To regenerate mocks after interface changes:

```bash
mockery
```

## Usage Patterns

### 1. Basic Mock Setup

```go
func TestExpenseService_CreateExpense(t *testing.T) {
    // Create mock with automatic cleanup
    mockRepo := mocks.NewMockExpenseRepository(t)
    service := services.NewExpenseService(mockRepo)
    
    expense := &models.Expense{
        Amount:      25.50,
        Category:    "Food",
        Subcategory: "Lunch",
        Date:        "2025-01-15",
    }
    
    // Set expectations
    mockRepo.EXPECT().Create(expense).Return(nil).Once()
    
    // Test
    err := service.CreateExpense(expense)
    
    // Assert
    assert.NoError(t, err)
    // mockRepo.AssertExpectations(t) called automatically
}
```

### 2. Error Testing

```go
func TestExpenseService_CreateExpense_Error(t *testing.T) {
    mockRepo := mocks.NewMockExpenseRepository(t)
    service := services.NewExpenseService(mockRepo)
    
    expense := &models.Expense{...}
    expectedError := errors.New("database error")
    
    // Expect error return
    mockRepo.EXPECT().Create(expense).Return(expectedError).Once()
    
    err := service.CreateExpense(expense)
    
    assert.Error(t, err)
    assert.Equal(t, expectedError, err)
}
```

### 3. Return Value Configuration

```go
func TestExpenseService_GetExpenses(t *testing.T) {
    mockRepo := mocks.NewMockExpenseRepository(t)
    service := services.NewExpenseService(mockRepo)
    
    expectedExpenses := []models.Expense{
        {ID: 1, Amount: 25.50, Category: "Food"},
        {ID: 2, Amount: 30.00, Category: "Transport"},
    }
    
    filters := map[string]string{"month": "1", "year": "2025"}
    
    // Configure return values
    mockRepo.EXPECT().GetAll(filters).Return(expectedExpenses, nil).Once()
    
    expenses, err := service.GetExpenses(filters)
    
    assert.NoError(t, err)
    assert.Equal(t, expectedExpenses, expenses)
    assert.Len(t, expenses, 2)
}
```

### 4. Argument Matching

```go
func TestExpenseService_ArgumentMatching(t *testing.T) {
    mockRepo := mocks.NewMockExpenseRepository(t)
    service := services.NewExpenseService(mockRepo)
    
    // Match any argument of specific type
    mockRepo.EXPECT().Create(mock.AnythingOfType("*models.Expense")).Return(nil)
    
    // Custom argument matching
    mockRepo.EXPECT().GetAll(mock.MatchedBy(func(filters map[string]string) bool {
        return filters["category"] == "Food"
    })).Return([]models.Expense{}, nil)
    
    // Match any argument
    mockRepo.EXPECT().GetAll(mock.Anything).Return([]models.Expense{}, nil)
}
```

### 5. Side Effects with Run

```go
func TestExpenseService_WithSideEffects(t *testing.T) {
    mockRepo := mocks.NewMockExpenseRepository(t)
    service := services.NewExpenseService(mockRepo)
    
    expense := &models.Expense{Amount: 25.50, Category: "Food"}
    
    // Simulate side effects
    mockRepo.EXPECT().Create(expense).
        Run(func(exp *models.Expense) {
            // Simulate what real repository does
            exp.ID = 42
            exp.CreatedAt = "2025-01-15 12:00:00"
        }).
        Return(nil).
        Once()
    
    err := service.CreateExpense(expense)
    
    assert.NoError(t, err)
    assert.Equal(t, 42, expense.ID)
    assert.Equal(t, "2025-01-15 12:00:00", expense.CreatedAt)
}
```

### 6. Multiple Calls

```go
func TestExpenseService_MultipleCalls(t *testing.T) {
    mockRepo := mocks.NewMockExpenseRepository(t)
    service := services.NewExpenseService(mockRepo)
    
    // Set up multiple expectations
    mockRepo.EXPECT().Create(mock.AnythingOfType("*models.Expense")).Return(nil).Times(2)
    mockRepo.EXPECT().GetAll(mock.Anything).Return([]models.Expense{}, nil).Once()
    
    // Make multiple calls
    service.CreateExpense(&models.Expense{...})
    service.CreateExpense(&models.Expense{...})
    service.GetExpenses(map[string]string{})
}
```

### 7. HTTP Handler Testing

```go
func TestExpenseHandler_CreateExpense(t *testing.T) {
    mockService := serviceMocks.NewMockExpenseService(t)
    handler := handlers.NewExpenseHandler(mockService)
    
    // Set up service mock
    mockService.EXPECT().CreateExpense(mock.AnythingOfType("*models.Expense")).
        Return(nil).
        Run(func(expense *models.Expense) {
            expense.ID = 1 // Simulate ID assignment
        }).
        Once()
    
    // Create HTTP request
    body := `{"amount":25.50,"category":"Food","subcategory":"Lunch","date":"2025-01-15"}`
    req := httptest.NewRequest("POST", "/expenses", strings.NewReader(body))
    req.Header.Set("Content-Type", "application/json")
    w := httptest.NewRecorder()
    
    // Test handler
    handler.CreateExpense(w, req)
    
    // Assert HTTP response
    assert.Equal(t, http.StatusCreated, w.Code)
    assert.Contains(t, w.Body.String(), `"id":1`)
}
```

## Advanced Features

### Call Verification

```go
// Verify exact number of calls
mockRepo.EXPECT().Create(mock.Anything).Return(nil).Times(3)

// Verify at least/most calls
mockRepo.EXPECT().Create(mock.Anything).Return(nil).AtLeast(1)
mockRepo.EXPECT().Create(mock.Anything).Return(nil).AtMost(5)

// Verify no calls
mockRepo.EXPECT().Create(mock.Anything).Return(nil).Times(0) // or .Never()
```

### Conditional Returns

```go
mockRepo.EXPECT().Create(mock.AnythingOfType("*models.Expense")).
    Return(func(expense *models.Expense) error {
        if expense.Amount <= 0 {
            return errors.New("invalid amount")
        }
        return nil
    })
```

### Panic on Unexpected Calls

```go
// Mockery automatically panics on unexpected calls
// This helps catch missing expectations
```

## Best Practices

### 1. Use Constructor with Testing.T

Always use the constructor that takes `testing.T`:

```go
// ✅ Good - automatic cleanup and assertion
mockRepo := mocks.NewMockExpenseRepository(t)

// ❌ Avoid - manual cleanup required
mockRepo := &mocks.MockExpenseRepository{}
```

### 2. Set Expectations Before Acting

```go
// ✅ Good - expectations first
mockRepo.EXPECT().Create(expense).Return(nil)
err := service.CreateExpense(expense)

// ❌ Bad - acting before expectations
err := service.CreateExpense(expense)
mockRepo.EXPECT().Create(expense).Return(nil)
```

### 3. Use Specific Matchers When Possible

```go
// ✅ Good - specific matching
mockRepo.EXPECT().Create(expense).Return(nil)

// ⚠️ Less specific but sometimes necessary
mockRepo.EXPECT().Create(mock.AnythingOfType("*models.Expense")).Return(nil)
```

### 4. Verify Call Counts

```go
// ✅ Good - explicit about expected calls
mockRepo.EXPECT().Create(mock.Anything).Return(nil).Once()

// ⚠️ Less explicit - defaults to at least once
mockRepo.EXPECT().Create(mock.Anything).Return(nil)
```

## Running Tests

```bash
# Run all tests
go test ./...

# Run specific test file
go test ./examples/mockery_usage_test.go

# Run with verbose output
go test -v ./examples/mockery_usage_test.go

# Run specific test
go test -run TestExpenseService_CreateExpense_Success ./examples/
```

## Dependencies

The generated mocks require:

```go
import (
    "github.com/stretchr/testify/mock"
    "github.com/stretchr/testify/assert" // for assertions in tests
)
```

## Comparison with Manual Mocks

| Feature | Manual Mocks | Mockery Mocks |
|---------|--------------|---------------|
| **Generation** | Manual coding | Automatic |
| **Call Tracking** | Manual implementation | Built-in |
| **Argument Matching** | Manual validation | Powerful matchers |
| **Return Configuration** | Manual setup | Fluent API |
| **Expectation Verification** | Manual assertions | Automatic |
| **Maintenance** | High effort | Low effort |
| **Learning Curve** | Low | Medium |

## Example Test File

See `examples/mockery_usage_test.go` for comprehensive examples of all patterns and features.

This Mockery-based approach provides more robust and maintainable tests with less boilerplate code!
