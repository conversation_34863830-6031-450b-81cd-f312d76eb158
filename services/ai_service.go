package services

import (
	"context"
	"encoding/json"
	"fmt"
	"go-expense/models"
	"regexp"
	"time"

	"github.com/sashabaranov/go-openai"
)

// ParsedExpense represents the result of AI parsing
type ParsedExpense struct {
	Amount           float64 `json:"amount"`
	Category         string  `json:"category"`
	Subcategory      string  `json:"subcategory"`
	Description      string  `json:"description"`
	Date             string  `json:"date"`
	AdditionalAmount float64 `json:"additional_amount"`
	Confidence       float64 `json:"confidence"` // 0-1 scale
}

// AIServiceInterface defines the contract for AI expense parsing
type AIServiceInterface interface {
	ParseExpenseText(text string) (*ParsedExpense, error)
}

// AIService handles AI-powered expense parsing
type AIService struct {
	client *openai.Client
}

// NewAIService creates a new AI service instance
func NewAIService(apiKey string) AIServiceInterface {
	client := openai.NewClient(apiKey)
	return &AIService{
		client: client,
	}
}

// ParseExpenseText parses Indonesian text into structured expense data
func (s *AIService) ParseExpenseText(text string) (*ParsedExpense, error) {
	if text == "" {
		return nil, fmt.Errorf("input text cannot be empty")
	}

	// Create the prompt for OpenAI
	prompt := s.createPrompt(text)

	// Call OpenAI API
	resp, err := s.client.CreateChatCompletion(
		context.Background(),
		openai.ChatCompletionRequest{
			Model: openai.GPT3Dot5Turbo,
			Messages: []openai.ChatCompletionMessage{
				{
					Role:    openai.ChatMessageRoleSystem,
					Content: s.getSystemPrompt(),
				},
				{
					Role:    openai.ChatMessageRoleUser,
					Content: prompt,
				},
			},
			MaxTokens:   300,
			Temperature: 0.1, // Low temperature for consistent parsing
		},
	)

	if err != nil {
		return nil, fmt.Errorf("failed to call OpenAI API: %w", err)
	}

	if len(resp.Choices) == 0 {
		return nil, fmt.Errorf("no response from OpenAI API")
	}

	// Parse the JSON response
	responseText := resp.Choices[0].Message.Content
	var parsed ParsedExpense
	if err := json.Unmarshal([]byte(responseText), &parsed); err != nil {
		return nil, fmt.Errorf("failed to parse AI response: %w", err)
	}

	// Validate and normalize the parsed data
	if err := s.validateAndNormalize(&parsed); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	return &parsed, nil
}

// getSystemPrompt returns the system prompt for expense parsing
func (s *AIService) getSystemPrompt() string {
	return `You are an expert at parsing Indonesian expense descriptions into structured data.

Available categories (map Indonesian terms to these exact categories):
- Food (makanan, makan, minum, kopi, nasi, ayam, etc.)
- Transportation (transportasi, bensin, ojek, grab, taxi, bus, etc.)
- Entertainment (hiburan, film, game, musik, etc.)
- Shopping (belanja, baju, sepatu, elektronik, etc.)
- Bills (tagihan, listrik, air, internet, pulsa, etc.)
- Healthcare (kesehatan, dokter, obat, rumah sakit, etc.)
- Education (pendidikan, sekolah, kursus, buku, etc.)
- Travel (perjalanan, hotel, tiket, liburan, etc.)
- Other (lainnya, misc, etc.)

Indonesian currency patterns to recognize:
- "ribu" or "rb" = thousand (e.g., "15 ribu" = 15000)
- "juta" = million
- Numbers with dots as thousand separators (e.g., "25.000")
- "rupiah", "rp", or just numbers

Date patterns:
- "hari ini" = today
- "kemarin" = yesterday  
- "tadi pagi/siang/sore/malam" = today
- Specific dates in Indonesian format

You must respond with ONLY valid JSON in this exact format:
{
  "amount": 25000,
  "category": "Food",
  "subcategory": "Lunch",
  "description": "Kopi di Starbucks",
  "date": "2025-01-15",
  "additional_amount": 0,
  "confidence": 0.9
}

Rules:
- amount must be a number (convert Indonesian currency terms)
- category must be one of the exact categories listed above
- subcategory should be a simple English term (e.g., "Lunch", "Gas", "Coffee")
- description should be clean Indonesian/English description
- date must be in YYYY-MM-DD format
- additional_amount is for tips, tax, etc. (usually 0)
- confidence is 0-1 (how confident you are in the parsing)
- If you cannot parse clearly, set confidence below 0.7`
}

// createPrompt creates the user prompt for parsing
func (s *AIService) createPrompt(text string) string {
	today := time.Now().Format("2006-01-02")
	return fmt.Sprintf(`Parse this Indonesian expense text into structured data:

Text: "%s"

Today's date is: %s

Respond with ONLY the JSON object, no other text.`, text, today)
}

// validateAndNormalize validates and normalizes the parsed expense data
func (s *AIService) validateAndNormalize(parsed *ParsedExpense) error {
	// Validate amount
	if parsed.Amount <= 0 {
		return fmt.Errorf("amount must be greater than 0")
	}

	// Validate category (must be one of the predefined categories)
	validCategories := []string{
		"Food", "Transportation", "Entertainment", "Shopping",
		"Bills", "Healthcare", "Education", "Travel", "Other",
	}

	categoryValid := false
	for _, cat := range validCategories {
		if parsed.Category == cat {
			categoryValid = true
			break
		}
	}
	if !categoryValid {
		parsed.Category = "Other" // Default to Other if invalid
	}

	// Validate date format
	if !isValidDateFormat(parsed.Date) {
		parsed.Date = time.Now().Format("2006-01-02") // Default to today
	}

	// Ensure subcategory is not empty
	if parsed.Subcategory == "" {
		parsed.Subcategory = "General"
	}

	// Ensure description is not empty
	if parsed.Description == "" {
		parsed.Description = "AI parsed expense"
	}

	// Validate confidence
	if parsed.Confidence < 0 || parsed.Confidence > 1 {
		parsed.Confidence = 0.5 // Default confidence
	}

	// Check minimum confidence threshold
	if parsed.Confidence < 0.7 {
		return fmt.Errorf("AI parsing confidence too low (%.2f). Please provide clearer expense description", parsed.Confidence)
	}

	return nil
}

// isValidDateFormat validates date format YYYY-MM-DD
func isValidDateFormat(dateStr string) bool {
	// Check format with regex
	dateRegex := regexp.MustCompile(`^\d{4}-\d{2}-\d{2}$`)
	if !dateRegex.MatchString(dateStr) {
		return false
	}

	// Parse date to ensure it's valid
	_, err := time.Parse("2006-01-02", dateStr)
	return err == nil
}

// ConvertToExpense converts ParsedExpense to models.Expense
func (parsed *ParsedExpense) ConvertToExpense() *models.Expense {
	return &models.Expense{
		Amount:           parsed.Amount,
		Category:         parsed.Category,
		Subcategory:      parsed.Subcategory,
		Description:      parsed.Description,
		Date:             parsed.Date,
		AdditionalAmount: parsed.AdditionalAmount,
	}
}
