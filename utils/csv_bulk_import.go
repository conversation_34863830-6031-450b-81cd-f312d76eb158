package utils

import (
    "database/sql"
    "encoding/csv"
    "fmt"
    "os"
    "strconv"
    "strings"
)

// BulkImportCSV imports expenses from CSV using a single transaction
func BulkImportCSV(filePath string, db *sql.DB) (int, error) {
    file, err := os.Open(filePath)
    if err != nil {
        return 0, err
    }
    defer file.Close()

    reader := csv.NewReader(file)
    records, err := reader.ReadAll()
    if err != nil {
        return 0, err
    }

    tx, err := db.Begin()
    if err != nil {
        return 0, err
    }
    defer tx.Rollback()

    stmt, err := tx.Prepare(`
        INSERT INTO expenses (amount, category, subcategory, description, date, additional_amount)
        VALUES (?, ?, ?, ?, ?, ?)
    `)
    if err != nil {
        return 0, err
    }
    defer stmt.Close()

    importCount := 0
    for i, record := range records {
        if i == 0 {
            continue // Skip header
        }

        // Parse date from DD/MM/YYYY to YYYY-MM-DD
        dateParts := strings.Split(record[0], "/")
        if len(dateParts) != 3 {
            continue
        }
        formattedDate := fmt.Sprintf("%s-%s-%s", dateParts[2], dateParts[1], dateParts[0])

        // Parse amounts
        amount, err := strconv.ParseFloat(record[4], 64)
        if err != nil {
            continue
        }
        
        additionalAmount, err := strconv.ParseFloat(record[5], 64)
        if err != nil {
            continue
        }

        _, err = stmt.Exec(amount, record[1], record[2], record[3], formattedDate, additionalAmount)
        if err != nil {
            return importCount, err
        }
        importCount++
    }

    if err := tx.Commit(); err != nil {
        return 0, err
    }

    return importCount, nil
}