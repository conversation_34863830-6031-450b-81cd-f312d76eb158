// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	models "go-expense/models"

	mock "github.com/stretchr/testify/mock"
)

// MockExpenseRepository is an autogenerated mock type for the ExpenseRepositoryInterface type
type MockExpenseRepository struct {
	mock.Mock
}

type MockExpenseRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockExpenseRepository) EXPECT() *MockExpenseRepository_Expecter {
	return &MockExpenseRepository_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: expense
func (_m *MockExpenseRepository) Create(expense *models.Expense) error {
	ret := _m.Called(expense)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*models.Expense) error); ok {
		r0 = rf(expense)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockExpenseRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockExpenseRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - expense *models.Expense
func (_e *MockExpenseRepository_Expecter) Create(expense interface{}) *MockExpenseRepository_Create_Call {
	return &MockExpenseRepository_Create_Call{Call: _e.mock.On("Create", expense)}
}

func (_c *MockExpenseRepository_Create_Call) Run(run func(expense *models.Expense)) *MockExpenseRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*models.Expense))
	})
	return _c
}

func (_c *MockExpenseRepository_Create_Call) Return(_a0 error) *MockExpenseRepository_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockExpenseRepository_Create_Call) RunAndReturn(run func(*models.Expense) error) *MockExpenseRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// CreateTable provides a mock function with no fields
func (_m *MockExpenseRepository) CreateTable() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for CreateTable")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockExpenseRepository_CreateTable_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateTable'
type MockExpenseRepository_CreateTable_Call struct {
	*mock.Call
}

// CreateTable is a helper method to define mock.On call
func (_e *MockExpenseRepository_Expecter) CreateTable() *MockExpenseRepository_CreateTable_Call {
	return &MockExpenseRepository_CreateTable_Call{Call: _e.mock.On("CreateTable")}
}

func (_c *MockExpenseRepository_CreateTable_Call) Run(run func()) *MockExpenseRepository_CreateTable_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockExpenseRepository_CreateTable_Call) Return(_a0 error) *MockExpenseRepository_CreateTable_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockExpenseRepository_CreateTable_Call) RunAndReturn(run func() error) *MockExpenseRepository_CreateTable_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function with given fields: id
func (_m *MockExpenseRepository) Delete(id int) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockExpenseRepository_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type MockExpenseRepository_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - id int
func (_e *MockExpenseRepository_Expecter) Delete(id interface{}) *MockExpenseRepository_Delete_Call {
	return &MockExpenseRepository_Delete_Call{Call: _e.mock.On("Delete", id)}
}

func (_c *MockExpenseRepository_Delete_Call) Run(run func(id int)) *MockExpenseRepository_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int))
	})
	return _c
}

func (_c *MockExpenseRepository_Delete_Call) Return(_a0 error) *MockExpenseRepository_Delete_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockExpenseRepository_Delete_Call) RunAndReturn(run func(int) error) *MockExpenseRepository_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// GetAll provides a mock function with given fields: filters
func (_m *MockExpenseRepository) GetAll(filters map[string]string) ([]models.Expense, error) {
	ret := _m.Called(filters)

	if len(ret) == 0 {
		panic("no return value specified for GetAll")
	}

	var r0 []models.Expense
	var r1 error
	if rf, ok := ret.Get(0).(func(map[string]string) ([]models.Expense, error)); ok {
		return rf(filters)
	}
	if rf, ok := ret.Get(0).(func(map[string]string) []models.Expense); ok {
		r0 = rf(filters)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Expense)
		}
	}

	if rf, ok := ret.Get(1).(func(map[string]string) error); ok {
		r1 = rf(filters)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockExpenseRepository_GetAll_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAll'
type MockExpenseRepository_GetAll_Call struct {
	*mock.Call
}

// GetAll is a helper method to define mock.On call
//   - filters map[string]string
func (_e *MockExpenseRepository_Expecter) GetAll(filters interface{}) *MockExpenseRepository_GetAll_Call {
	return &MockExpenseRepository_GetAll_Call{Call: _e.mock.On("GetAll", filters)}
}

func (_c *MockExpenseRepository_GetAll_Call) Run(run func(filters map[string]string)) *MockExpenseRepository_GetAll_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(map[string]string))
	})
	return _c
}

func (_c *MockExpenseRepository_GetAll_Call) Return(_a0 []models.Expense, _a1 error) *MockExpenseRepository_GetAll_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockExpenseRepository_GetAll_Call) RunAndReturn(run func(map[string]string) ([]models.Expense, error)) *MockExpenseRepository_GetAll_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function with given fields: id
func (_m *MockExpenseRepository) GetByID(id int) (*models.Expense, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *models.Expense
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*models.Expense, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *models.Expense); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Expense)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockExpenseRepository_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type MockExpenseRepository_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - id int
func (_e *MockExpenseRepository_Expecter) GetByID(id interface{}) *MockExpenseRepository_GetByID_Call {
	return &MockExpenseRepository_GetByID_Call{Call: _e.mock.On("GetByID", id)}
}

func (_c *MockExpenseRepository_GetByID_Call) Run(run func(id int)) *MockExpenseRepository_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int))
	})
	return _c
}

func (_c *MockExpenseRepository_GetByID_Call) Return(_a0 *models.Expense, _a1 error) *MockExpenseRepository_GetByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockExpenseRepository_GetByID_Call) RunAndReturn(run func(int) (*models.Expense, error)) *MockExpenseRepository_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetMonthlySummary provides a mock function with given fields: month, year
func (_m *MockExpenseRepository) GetMonthlySummary(month int, year int) (*models.MonthlySummary, error) {
	ret := _m.Called(month, year)

	if len(ret) == 0 {
		panic("no return value specified for GetMonthlySummary")
	}

	var r0 *models.MonthlySummary
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int) (*models.MonthlySummary, error)); ok {
		return rf(month, year)
	}
	if rf, ok := ret.Get(0).(func(int, int) *models.MonthlySummary); ok {
		r0 = rf(month, year)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.MonthlySummary)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(month, year)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockExpenseRepository_GetMonthlySummary_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMonthlySummary'
type MockExpenseRepository_GetMonthlySummary_Call struct {
	*mock.Call
}

// GetMonthlySummary is a helper method to define mock.On call
//   - month int
//   - year int
func (_e *MockExpenseRepository_Expecter) GetMonthlySummary(month interface{}, year interface{}) *MockExpenseRepository_GetMonthlySummary_Call {
	return &MockExpenseRepository_GetMonthlySummary_Call{Call: _e.mock.On("GetMonthlySummary", month, year)}
}

func (_c *MockExpenseRepository_GetMonthlySummary_Call) Run(run func(month int, year int)) *MockExpenseRepository_GetMonthlySummary_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int), args[1].(int))
	})
	return _c
}

func (_c *MockExpenseRepository_GetMonthlySummary_Call) Return(_a0 *models.MonthlySummary, _a1 error) *MockExpenseRepository_GetMonthlySummary_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockExpenseRepository_GetMonthlySummary_Call) RunAndReturn(run func(int, int) (*models.MonthlySummary, error)) *MockExpenseRepository_GetMonthlySummary_Call {
	_c.Call.Return(run)
	return _c
}

// GetYearlySummary provides a mock function with given fields: year
func (_m *MockExpenseRepository) GetYearlySummary(year int) (*models.YearlySummary, error) {
	ret := _m.Called(year)

	if len(ret) == 0 {
		panic("no return value specified for GetYearlySummary")
	}

	var r0 *models.YearlySummary
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*models.YearlySummary, error)); ok {
		return rf(year)
	}
	if rf, ok := ret.Get(0).(func(int) *models.YearlySummary); ok {
		r0 = rf(year)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.YearlySummary)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(year)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockExpenseRepository_GetYearlySummary_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetYearlySummary'
type MockExpenseRepository_GetYearlySummary_Call struct {
	*mock.Call
}

// GetYearlySummary is a helper method to define mock.On call
//   - year int
func (_e *MockExpenseRepository_Expecter) GetYearlySummary(year interface{}) *MockExpenseRepository_GetYearlySummary_Call {
	return &MockExpenseRepository_GetYearlySummary_Call{Call: _e.mock.On("GetYearlySummary", year)}
}

func (_c *MockExpenseRepository_GetYearlySummary_Call) Run(run func(year int)) *MockExpenseRepository_GetYearlySummary_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int))
	})
	return _c
}

func (_c *MockExpenseRepository_GetYearlySummary_Call) Return(_a0 *models.YearlySummary, _a1 error) *MockExpenseRepository_GetYearlySummary_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockExpenseRepository_GetYearlySummary_Call) RunAndReturn(run func(int) (*models.YearlySummary, error)) *MockExpenseRepository_GetYearlySummary_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function with given fields: expense
func (_m *MockExpenseRepository) Update(expense *models.Expense) error {
	ret := _m.Called(expense)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*models.Expense) error); ok {
		r0 = rf(expense)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockExpenseRepository_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type MockExpenseRepository_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - expense *models.Expense
func (_e *MockExpenseRepository_Expecter) Update(expense interface{}) *MockExpenseRepository_Update_Call {
	return &MockExpenseRepository_Update_Call{Call: _e.mock.On("Update", expense)}
}

func (_c *MockExpenseRepository_Update_Call) Run(run func(expense *models.Expense)) *MockExpenseRepository_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*models.Expense))
	})
	return _c
}

func (_c *MockExpenseRepository_Update_Call) Return(_a0 error) *MockExpenseRepository_Update_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockExpenseRepository_Update_Call) RunAndReturn(run func(*models.Expense) error) *MockExpenseRepository_Update_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockExpenseRepository creates a new instance of MockExpenseRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockExpenseRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockExpenseRepository {
	mock := &MockExpenseRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
