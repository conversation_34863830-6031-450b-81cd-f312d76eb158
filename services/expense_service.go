package services

import (
	"fmt"
	"go-expense/models"
	"go-expense/utils"
	"regexp"
	"strconv"
	"time"

	"github.com/xuri/excelize/v2"
)

// ExpenseServiceInterface defines the contract for expense business logic
type ExpenseServiceInterface interface {
	CreateExpense(expense *models.Expense) error
	GetExpenseByID(id string) (*models.Expense, error)
	UpdateExpense(expense *models.Expense) error
	DeleteExpense(id string) error
	GetExpenses(filters map[string]string) ([]models.Expense, error)
	ExportExpensesToExcel(monthStr, yearStr string) (*excelize.File, error)
	GetMonthlySummary(monthStr, yearStr string) (*models.MonthlySummary, error)
	GetYearlySummary(yearStr string) (*models.YearlySummary, error)
}

// ExpenseService handles business logic for expenses
type ExpenseService struct {
	repo models.ExpenseRepositoryInterface
}

// NewExpenseService creates a new expense service
func NewExpenseService(repo models.ExpenseRepositoryInterface) ExpenseServiceInterface {
	return &ExpenseService{repo: repo}
}

// CreateExpense validates and creates a new expense
func (s *ExpenseService) CreateExpense(expense *models.Expense) error {
	// Validate required fields
	if expense.Amount <= 0 {
		return fmt.Errorf("amount must be greater than 0")
	}

	if expense.Category == "" {
		return fmt.Errorf("category is required")
	}

	if expense.Subcategory == "" {
		return fmt.Errorf("subcategory is required")
	}

	// Validate date format (YYYY-MM-DD)
	if !isValidDate(expense.Date) {
		return fmt.Errorf("date must be in YYYY-MM-DD format")
	}

	// Create expense through repository
	return s.repo.Create(expense)
}

// GetExpenseByID validates ID and retrieves a single expense
func (s *ExpenseService) GetExpenseByID(id string) (*models.Expense, error) {
	// Validate ID parameter
	if id == "" {
		return nil, fmt.Errorf("expense ID is required")
	}

	// Convert string ID to int
	expenseID, err := strconv.Atoi(id)
	if err != nil || expenseID <= 0 {
		return nil, fmt.Errorf("invalid expense ID: must be a positive integer")
	}

	// Get expense through repository
	return s.repo.GetByID(expenseID)
}

// UpdateExpense validates and updates an existing expense
func (s *ExpenseService) UpdateExpense(expense *models.Expense) error {
	// Validate required fields
	if expense.ID <= 0 {
		return fmt.Errorf("expense ID must be greater than 0")
	}

	if expense.Amount <= 0 {
		return fmt.Errorf("amount must be greater than 0")
	}

	if expense.Category == "" {
		return fmt.Errorf("category is required")
	}

	if expense.Subcategory == "" {
		return fmt.Errorf("subcategory is required")
	}

	// Validate date format (YYYY-MM-DD)
	if !isValidDate(expense.Date) {
		return fmt.Errorf("date must be in YYYY-MM-DD format")
	}

	// Update expense through repository
	return s.repo.Update(expense)
}

// DeleteExpense validates ID and deletes an expense
func (s *ExpenseService) DeleteExpense(id string) error {
	// Validate ID parameter
	if id == "" {
		return fmt.Errorf("expense ID is required")
	}

	// Convert string ID to int
	expenseID, err := strconv.Atoi(id)
	if err != nil || expenseID <= 0 {
		return fmt.Errorf("invalid expense ID: must be a positive integer")
	}

	// Delete expense through repository
	return s.repo.Delete(expenseID)
}

// GetExpenses retrieves expenses with validation and filtering
func (s *ExpenseService) GetExpenses(filters map[string]string) ([]models.Expense, error) {
	// Validate filters
	if month, ok := filters["month"]; ok {
		if monthInt, err := strconv.Atoi(month); err != nil || monthInt < 1 || monthInt > 12 {
			return nil, fmt.Errorf("invalid month parameter: must be between 1 and 12")
		}
	}

	if year, ok := filters["year"]; ok {
		if yearInt, err := strconv.Atoi(year); err != nil || yearInt < 1900 || yearInt > 2100 {
			return nil, fmt.Errorf("invalid year parameter: must be between 1900 and 2100")
		}
	}

	// Get expenses through repository
	return s.repo.GetAll(filters)
}

// ExportExpensesToExcel validates parameters and exports expenses to Excel
func (s *ExpenseService) ExportExpensesToExcel(monthStr, yearStr string) (*excelize.File, error) {
	// Validate month and year parameters
	if monthStr == "" || yearStr == "" {
		return nil, fmt.Errorf("month and year parameters are required")
	}

	// Validate month and year values
	_, _, err := utils.ValidateMonthYear(monthStr, yearStr)
	if err != nil {
		return nil, err
	}

	// Get expenses for the specified month and year
	filters := map[string]string{
		"month": monthStr,
		"year":  yearStr,
	}

	expenses, err := s.repo.GetAll(filters)
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve expenses: %w", err)
	}

	// Create Excel file
	excelFile, err := utils.ExportToExcel(expenses, monthStr, yearStr)
	if err != nil {
		return nil, fmt.Errorf("failed to create Excel file: %w", err)
	}

	return excelFile, nil
}

// GetMonthlySummary validates parameters and retrieves monthly summary with weekly breakdown
func (s *ExpenseService) GetMonthlySummary(monthStr, yearStr string) (*models.MonthlySummary, error) {
	// Validate month and year parameters
	if monthStr == "" || yearStr == "" {
		return nil, fmt.Errorf("month and year parameters are required")
	}

	// Validate month and year values
	month, year, err := utils.ValidateMonthYear(monthStr, yearStr)
	if err != nil {
		return nil, err
	}

	// Get monthly summary through repository
	summary, err := s.repo.GetMonthlySummary(month, year)
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve monthly summary: %w", err)
	}

	return summary, nil
}

// GetYearlySummary validates parameters and retrieves yearly summary with monthly breakdown
func (s *ExpenseService) GetYearlySummary(yearStr string) (*models.YearlySummary, error) {
	// Validate year parameter
	if yearStr == "" {
		return nil, fmt.Errorf("year parameter is required")
	}

	// Validate year value
	year, err := strconv.Atoi(yearStr)
	if err != nil || year < 1900 || year > 2100 {
		return nil, fmt.Errorf("invalid year parameter: must be between 1900 and 2100")
	}

	// Get yearly summary through repository
	summary, err := s.repo.GetYearlySummary(year)
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve yearly summary: %w", err)
	}

	return summary, nil
}

// isValidDate validates date format YYYY-MM-DD
func isValidDate(dateStr string) bool {
	// Check format with regex
	dateRegex := regexp.MustCompile(`^\d{4}-\d{2}-\d{2}$`)
	if !dateRegex.MatchString(dateStr) {
		return false
	}

	// Parse date to ensure it's valid
	_, err := time.Parse("2006-01-02", dateStr)
	return err == nil
}
