# Mock Implementations

This document describes the mock implementations provided for testing the Go Expense Tracker application.

## Overview

The application provides mock implementations for all interfaces to enable comprehensive unit testing without external dependencies.

## Mock Packages

### 1. Repository Mock (`models/mock/`)

**File**: `models/mock/expense_repository_mock.go`

**Interface**: `models.ExpenseRepositoryInterface`

**Features**:
- In-memory storage for expenses
- Full CRUD operations
- Filtering support (month, year, category, subcategory)
- Error simulation capabilities
- Helper methods for testing

**Usage**:
```go
import "go-expense/models/mock"

// Create mock repository
mockRepo := mock.NewMockExpenseRepository()

// Use like real repository
err := mockRepo.CreateTable()
err = mockRepo.Create(&expense)
expenses, err := mockRepo.GetAll(filters)

// Configure errors for testing
mockRepo.(*mock.MockExpenseRepository).SetError(true, "database error")
```

### 2. Service Mock (`services/mock/`)

**File**: `services/mock/expense_service_mock.go`

**Interface**: `services.ExpenseServiceInterface`

**Features**:
- Business logic simulation
- Validation error simulation
- Excel export mock
- Granular error control per method
- Helper methods for testing

**Usage**:
```go
import serviceMock "go-expense/services/mock"

// Create mock service
mockService := serviceMock.NewMockExpenseService()

// Use like real service
err := mockService.CreateExpense(&expense)
expenses, err := mockService.GetExpenses(filters)
excelFile, err := mockService.ExportExpensesToExcel("1", "2025")

// Configure specific errors
mockService.(*serviceMock.MockExpenseService).SetCreateError(true, "validation failed")
```

## Mock Features

### Error Simulation

Both mocks support error simulation for testing error handling:

```go
// Repository mock - all operations will fail
mockRepo.SetError(true, "database connection failed")

// Service mock - granular error control
mockService.SetCreateError(true, "create operation failed")
mockService.SetGetError(true, "get operation failed")
mockService.SetExportError(true, "export operation failed")
```

### Helper Methods

Both mocks provide helper methods for test setup and verification:

```go
// Repository mock helpers
mockRepo.AddExpense(expense)           // Add expense directly
count := mockRepo.GetExpenseCount()    // Get total count
expense := mockRepo.GetExpenseByID(1)  // Get by ID
mockRepo.Clear()                       // Clear all data

// Service mock helpers (same methods available)
mockService.AddExpense(expense)
count := mockService.GetExpenseCount()
expense := mockService.GetExpenseByID(1)
mockService.Clear()
```

## Testing Patterns

### 1. Unit Testing with Repository Mock

Test service layer with mocked repository:

```go
func TestExpenseService_CreateExpense(t *testing.T) {
    // Arrange
    mockRepo := mock.NewMockExpenseRepository()
    service := services.NewExpenseService(mockRepo)
    
    expense := &models.Expense{
        Amount:      25.50,
        Category:    "Food",
        Subcategory: "Lunch",
        Date:        "2025-01-15",
    }
    
    // Act
    err := service.CreateExpense(expense)
    
    // Assert
    assert.NoError(t, err)
    assert.Equal(t, 1, expense.ID)
}
```

### 2. Unit Testing with Service Mock

Test handlers with mocked service:

```go
func TestExpenseHandler_CreateExpense(t *testing.T) {
    // Arrange
    mockService := serviceMock.NewMockExpenseService()
    handler := handlers.NewExpenseHandler(mockService)
    
    // Create HTTP request/response
    body := `{"amount":25.50,"category":"Food","subcategory":"Lunch","date":"2025-01-15"}`
    req := httptest.NewRequest("POST", "/expenses", strings.NewReader(body))
    req.Header.Set("Content-Type", "application/json")
    w := httptest.NewRecorder()
    
    // Act
    handler.CreateExpense(w, req)
    
    // Assert
    assert.Equal(t, http.StatusCreated, w.Code)
}
```

### 3. Error Testing

Test error handling scenarios:

```go
func TestExpenseService_CreateExpense_RepositoryError(t *testing.T) {
    // Arrange
    mockRepo := mock.NewMockExpenseRepository().(*mock.MockExpenseRepository)
    mockRepo.SetError(true, "database connection failed")
    service := services.NewExpenseService(mockRepo)
    
    expense := &models.Expense{
        Amount:      25.50,
        Category:    "Food",
        Subcategory: "Lunch",
        Date:        "2025-01-15",
    }
    
    // Act
    err := service.CreateExpense(expense)
    
    // Assert
    assert.Error(t, err)
    assert.Contains(t, err.Error(), "database connection failed")
}
```

### 4. Validation Testing

Test business logic validation:

```go
func TestExpenseService_CreateExpense_InvalidAmount(t *testing.T) {
    // Arrange
    mockRepo := mock.NewMockExpenseRepository()
    service := services.NewExpenseService(mockRepo)
    
    expense := &models.Expense{
        Amount:      -10, // Invalid amount
        Category:    "Food",
        Subcategory: "Lunch",
        Date:        "2025-01-15",
    }
    
    // Act
    err := service.CreateExpense(expense)
    
    // Assert
    assert.Error(t, err)
    assert.Equal(t, "amount must be greater than 0", err.Error())
}
```

## Integration Testing

### Layered Testing

Test real components with mocked dependencies:

```go
func TestIntegration_ServiceWithMockRepository(t *testing.T) {
    // Use real service with mock repository
    mockRepo := mock.NewMockExpenseRepository()
    realService := services.NewExpenseService(mockRepo)
    
    // Test real service behavior
    expense := &models.Expense{
        Amount:      25.50,
        Category:    "Food",
        Subcategory: "Lunch",
        Date:        "2025-01-15",
    }
    
    err := realService.CreateExpense(expense)
    assert.NoError(t, err)
    
    // Verify through repository
    expenses, err := mockRepo.GetAll(map[string]string{})
    assert.NoError(t, err)
    assert.Len(t, expenses, 1)
}
```

## Best Practices

1. **Use Type Assertions**: Cast to concrete mock types to access helper methods:
   ```go
   mockRepo := mock.NewMockExpenseRepository().(*mock.MockExpenseRepository)
   ```

2. **Clear State**: Always clear mock state between tests:
   ```go
   mockRepo.Clear()
   ```

3. **Test Error Paths**: Use error simulation to test error handling:
   ```go
   mockRepo.SetError(true, "specific error message")
   ```

4. **Verify Interactions**: Use helper methods to verify mock interactions:
   ```go
   assert.Equal(t, 1, mockRepo.GetExpenseCount())
   ```

5. **Layer Testing**: Test different layers with appropriate mocks:
   - Handler tests: Use service mocks
   - Service tests: Use repository mocks
   - Integration tests: Mix real and mock components

## Example Usage

See `examples/mock_usage_example.go` for comprehensive examples of using all mock implementations.

## Running Examples

```bash
# The examples are provided as reference code
# To run them, you would need to create a main function that calls the example functions
```

This mock system enables comprehensive testing of all application layers without external dependencies like databases or file systems.
