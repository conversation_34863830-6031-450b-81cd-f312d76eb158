package handlers

import (
	"encoding/json"
	"fmt"
	"go-expense/models"
	"go-expense/services"
	"net/http"
	"strings"
)

// ExpenseHandler handles HTTP requests for expenses
type ExpenseHandler struct {
	service services.ExpenseServiceInterface
}

// NewExpenseHandler creates a new expense handler
func NewExpenseHandler(service services.ExpenseServiceInterface) *ExpenseHandler {
	return &ExpenseHandler{service: service}
}

// CreateExpense handles POST /expenses
func (h *ExpenseHandler) CreateExpense(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var expense models.Expense
	if err := json.NewDecoder(r.Body).Decode(&expense); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Delegate business logic to service
	if err := h.service.CreateExpense(&expense); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(expense)
}

// GetExpenses handles GET /expenses
func (h *ExpenseHandler) GetExpenses(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Parse query parameters
	filters := make(map[string]string)

	if month := r.URL.Query().Get("month"); month != "" {
		filters["month"] = month
	}

	if year := r.URL.Query().Get("year"); year != "" {
		filters["year"] = year
	}

	if category := r.URL.Query().Get("category"); category != "" {
		filters["category"] = category
	}

	if subcategory := r.URL.Query().Get("subcategory"); subcategory != "" {
		filters["subcategory"] = subcategory
	}

	// Delegate business logic to service
	expenses, err := h.service.GetExpenses(filters)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(expenses)
}

// DeleteExpense handles DELETE /expenses/{id}
func (h *ExpenseHandler) DeleteExpense(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodDelete {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Extract ID from URL path
	path := r.URL.Path
	if !strings.HasPrefix(path, "/expenses/delete/") {
		http.Error(w, "Invalid URL", http.StatusBadRequest)
		return
	}

	id := strings.TrimPrefix(path, "/expenses/delete/")
	if id == "" {
		http.Error(w, "Expense ID is required", http.StatusBadRequest)
		return
	}

	// Delegate business logic to service
	if err := h.service.DeleteExpense(id); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	w.WriteHeader(http.StatusNoContent)
}

// ExportExpenses handles GET /expenses/export
func (h *ExpenseHandler) ExportExpenses(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get month and year parameters
	monthStr := r.URL.Query().Get("month")
	yearStr := r.URL.Query().Get("year")

	// Delegate business logic to service
	excelFile, err := h.service.ExportExpensesToExcel(monthStr, yearStr)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Set headers for file download
	filename := fmt.Sprintf("expenses_%s_%s.xlsx", yearStr, monthStr)
	w.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))

	// Write Excel file to response
	if err := excelFile.Write(w); err != nil {
		http.Error(w, "Failed to write Excel file", http.StatusInternalServerError)
		return
	}
}

// GetMonthlySummary handles GET /expenses/summary/monthly
func (h *ExpenseHandler) GetMonthlySummary(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get month and year parameters
	monthStr := r.URL.Query().Get("month")
	yearStr := r.URL.Query().Get("year")

	// Delegate business logic to service
	summary, err := h.service.GetMonthlySummary(monthStr, yearStr)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(summary)
}

// GetYearlySummary handles GET /expenses/summary/yearly
func (h *ExpenseHandler) GetYearlySummary(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get year parameter
	yearStr := r.URL.Query().Get("year")

	// Delegate business logic to service
	summary, err := h.service.GetYearlySummary(yearStr)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(summary)
}
