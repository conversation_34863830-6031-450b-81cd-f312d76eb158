{{define "ai-input-content"}}
<div class="card">
    <h1>AI Expense Input</h1>
    <p>Describe your expense in Bahasa Indonesia and let AI parse it for you!</p>
    
    {{if .Error}}
    <div class="notification error">
        <strong>Error:</strong> {{.Error}}
    </div>
    {{end}}

    <form action="/ui/ai-parse" method="POST" class="expense-form">
        <div class="form-group">
            <label for="expense_text">Expense Description *</label>
            <textarea name="expense_text" id="expense_text" rows="4" required
                      {{if .Error}}disabled{{end}}
                      placeholder="Contoh: Beli kopi di Starbucks 25 ribu tadi pagi&#10;Makan siang di warteg 15rb kemarin&#10;Isi bensin motor 50.000 hari ini"></textarea>
            <small class="field-help">Describe your expense in Indonesian. Include amount, what you bought, and when.</small>
        </div>

        <div class="form-actions">
            <button type="submit" class="btn btn-primary" {{if .Error}}disabled{{end}}>Parse with AI</button>
            <a href="/ui" class="btn btn-secondary">Cancel</a>
        </div>
    </form>
</div>

<div class="card">
    <h2>Examples</h2>
    <div class="examples-grid">
        <div class="example-item">
            <strong>Food:</strong><br>
            "Beli kopi di Starbucks 25 ribu tadi pagi"
        </div>
        <div class="example-item">
            <strong>Transportation:</strong><br>
            "Isi bensin motor 50.000 hari ini"
        </div>
        <div class="example-item">
            <strong>Shopping:</strong><br>
            "Beli baju di mall 150rb kemarin"
        </div>
        <div class="example-item">
            <strong>Bills:</strong><br>
            "Bayar listrik 200.000 bulan ini"
        </div>
    </div>
</div>

<style>
.examples-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.example-item {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    border-left: 4px solid #007acc;
}

.example-item strong {
    color: #007acc;
}

textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: inherit;
    font-size: 1rem;
    resize: vertical;
    min-height: 100px;
}

textarea:focus {
    outline: none;
    border-color: #007acc;
    box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
}

.field-help {
    display: block;
    margin-top: 0.5rem;
    color: #666;
    font-size: 0.875rem;
}
</style>
{{end}}
