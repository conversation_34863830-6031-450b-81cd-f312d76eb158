with-expecter: true
dir: "{{.InterfaceDir}}/mocks"
outpkg: mocks
packages:
  go-expense/models:
    interfaces:
      ExpenseRepositoryInterface:
        config:
          mockname: MockExpenseRepository
          filename: expense_repository_mock.go
  go-expense/services:
    interfaces:
      ExpenseServiceInterface:
        config:
          mockname: MockExpenseService
          filename: expense_service_mock.go
