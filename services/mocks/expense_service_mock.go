// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	mock "github.com/stretchr/testify/mock"
	excelize "github.com/xuri/excelize/v2"

	models "go-expense/models"
)

// MockExpenseService is an autogenerated mock type for the ExpenseServiceInterface type
type MockExpenseService struct {
	mock.Mock
}

type MockExpenseService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockExpenseService) EXPECT() *MockExpenseService_Expecter {
	return &MockExpenseService_Expecter{mock: &_m.Mock}
}

// CreateExpense provides a mock function with given fields: expense
func (_m *MockExpenseService) CreateExpense(expense *models.Expense) error {
	ret := _m.Called(expense)

	if len(ret) == 0 {
		panic("no return value specified for CreateExpense")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*models.Expense) error); ok {
		r0 = rf(expense)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockExpenseService_CreateExpense_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateExpense'
type MockExpenseService_CreateExpense_Call struct {
	*mock.Call
}

// CreateExpense is a helper method to define mock.On call
//   - expense *models.Expense
func (_e *MockExpenseService_Expecter) CreateExpense(expense interface{}) *MockExpenseService_CreateExpense_Call {
	return &MockExpenseService_CreateExpense_Call{Call: _e.mock.On("CreateExpense", expense)}
}

func (_c *MockExpenseService_CreateExpense_Call) Run(run func(expense *models.Expense)) *MockExpenseService_CreateExpense_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*models.Expense))
	})
	return _c
}

func (_c *MockExpenseService_CreateExpense_Call) Return(_a0 error) *MockExpenseService_CreateExpense_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockExpenseService_CreateExpense_Call) RunAndReturn(run func(*models.Expense) error) *MockExpenseService_CreateExpense_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteExpense provides a mock function with given fields: id
func (_m *MockExpenseService) DeleteExpense(id string) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteExpense")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockExpenseService_DeleteExpense_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteExpense'
type MockExpenseService_DeleteExpense_Call struct {
	*mock.Call
}

// DeleteExpense is a helper method to define mock.On call
//   - id string
func (_e *MockExpenseService_Expecter) DeleteExpense(id interface{}) *MockExpenseService_DeleteExpense_Call {
	return &MockExpenseService_DeleteExpense_Call{Call: _e.mock.On("DeleteExpense", id)}
}

func (_c *MockExpenseService_DeleteExpense_Call) Run(run func(id string)) *MockExpenseService_DeleteExpense_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockExpenseService_DeleteExpense_Call) Return(_a0 error) *MockExpenseService_DeleteExpense_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockExpenseService_DeleteExpense_Call) RunAndReturn(run func(string) error) *MockExpenseService_DeleteExpense_Call {
	_c.Call.Return(run)
	return _c
}

// ExportExpensesToExcel provides a mock function with given fields: monthStr, yearStr
func (_m *MockExpenseService) ExportExpensesToExcel(monthStr string, yearStr string) (*excelize.File, error) {
	ret := _m.Called(monthStr, yearStr)

	if len(ret) == 0 {
		panic("no return value specified for ExportExpensesToExcel")
	}

	var r0 *excelize.File
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (*excelize.File, error)); ok {
		return rf(monthStr, yearStr)
	}
	if rf, ok := ret.Get(0).(func(string, string) *excelize.File); ok {
		r0 = rf(monthStr, yearStr)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*excelize.File)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(monthStr, yearStr)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockExpenseService_ExportExpensesToExcel_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ExportExpensesToExcel'
type MockExpenseService_ExportExpensesToExcel_Call struct {
	*mock.Call
}

// ExportExpensesToExcel is a helper method to define mock.On call
//   - monthStr string
//   - yearStr string
func (_e *MockExpenseService_Expecter) ExportExpensesToExcel(monthStr interface{}, yearStr interface{}) *MockExpenseService_ExportExpensesToExcel_Call {
	return &MockExpenseService_ExportExpensesToExcel_Call{Call: _e.mock.On("ExportExpensesToExcel", monthStr, yearStr)}
}

func (_c *MockExpenseService_ExportExpensesToExcel_Call) Run(run func(monthStr string, yearStr string)) *MockExpenseService_ExportExpensesToExcel_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *MockExpenseService_ExportExpensesToExcel_Call) Return(_a0 *excelize.File, _a1 error) *MockExpenseService_ExportExpensesToExcel_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockExpenseService_ExportExpensesToExcel_Call) RunAndReturn(run func(string, string) (*excelize.File, error)) *MockExpenseService_ExportExpensesToExcel_Call {
	_c.Call.Return(run)
	return _c
}

// GetExpenseByID provides a mock function with given fields: id
func (_m *MockExpenseService) GetExpenseByID(id string) (*models.Expense, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for GetExpenseByID")
	}

	var r0 *models.Expense
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*models.Expense, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(string) *models.Expense); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Expense)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockExpenseService_GetExpenseByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetExpenseByID'
type MockExpenseService_GetExpenseByID_Call struct {
	*mock.Call
}

// GetExpenseByID is a helper method to define mock.On call
//   - id string
func (_e *MockExpenseService_Expecter) GetExpenseByID(id interface{}) *MockExpenseService_GetExpenseByID_Call {
	return &MockExpenseService_GetExpenseByID_Call{Call: _e.mock.On("GetExpenseByID", id)}
}

func (_c *MockExpenseService_GetExpenseByID_Call) Run(run func(id string)) *MockExpenseService_GetExpenseByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockExpenseService_GetExpenseByID_Call) Return(_a0 *models.Expense, _a1 error) *MockExpenseService_GetExpenseByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockExpenseService_GetExpenseByID_Call) RunAndReturn(run func(string) (*models.Expense, error)) *MockExpenseService_GetExpenseByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetExpenses provides a mock function with given fields: filters
func (_m *MockExpenseService) GetExpenses(filters map[string]string) ([]models.Expense, error) {
	ret := _m.Called(filters)

	if len(ret) == 0 {
		panic("no return value specified for GetExpenses")
	}

	var r0 []models.Expense
	var r1 error
	if rf, ok := ret.Get(0).(func(map[string]string) ([]models.Expense, error)); ok {
		return rf(filters)
	}
	if rf, ok := ret.Get(0).(func(map[string]string) []models.Expense); ok {
		r0 = rf(filters)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Expense)
		}
	}

	if rf, ok := ret.Get(1).(func(map[string]string) error); ok {
		r1 = rf(filters)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockExpenseService_GetExpenses_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetExpenses'
type MockExpenseService_GetExpenses_Call struct {
	*mock.Call
}

// GetExpenses is a helper method to define mock.On call
//   - filters map[string]string
func (_e *MockExpenseService_Expecter) GetExpenses(filters interface{}) *MockExpenseService_GetExpenses_Call {
	return &MockExpenseService_GetExpenses_Call{Call: _e.mock.On("GetExpenses", filters)}
}

func (_c *MockExpenseService_GetExpenses_Call) Run(run func(filters map[string]string)) *MockExpenseService_GetExpenses_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(map[string]string))
	})
	return _c
}

func (_c *MockExpenseService_GetExpenses_Call) Return(_a0 []models.Expense, _a1 error) *MockExpenseService_GetExpenses_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockExpenseService_GetExpenses_Call) RunAndReturn(run func(map[string]string) ([]models.Expense, error)) *MockExpenseService_GetExpenses_Call {
	_c.Call.Return(run)
	return _c
}

// GetMonthlySummary provides a mock function with given fields: monthStr, yearStr
func (_m *MockExpenseService) GetMonthlySummary(monthStr string, yearStr string) (*models.MonthlySummary, error) {
	ret := _m.Called(monthStr, yearStr)

	if len(ret) == 0 {
		panic("no return value specified for GetMonthlySummary")
	}

	var r0 *models.MonthlySummary
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (*models.MonthlySummary, error)); ok {
		return rf(monthStr, yearStr)
	}
	if rf, ok := ret.Get(0).(func(string, string) *models.MonthlySummary); ok {
		r0 = rf(monthStr, yearStr)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.MonthlySummary)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(monthStr, yearStr)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockExpenseService_GetMonthlySummary_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMonthlySummary'
type MockExpenseService_GetMonthlySummary_Call struct {
	*mock.Call
}

// GetMonthlySummary is a helper method to define mock.On call
//   - monthStr string
//   - yearStr string
func (_e *MockExpenseService_Expecter) GetMonthlySummary(monthStr interface{}, yearStr interface{}) *MockExpenseService_GetMonthlySummary_Call {
	return &MockExpenseService_GetMonthlySummary_Call{Call: _e.mock.On("GetMonthlySummary", monthStr, yearStr)}
}

func (_c *MockExpenseService_GetMonthlySummary_Call) Run(run func(monthStr string, yearStr string)) *MockExpenseService_GetMonthlySummary_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *MockExpenseService_GetMonthlySummary_Call) Return(_a0 *models.MonthlySummary, _a1 error) *MockExpenseService_GetMonthlySummary_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockExpenseService_GetMonthlySummary_Call) RunAndReturn(run func(string, string) (*models.MonthlySummary, error)) *MockExpenseService_GetMonthlySummary_Call {
	_c.Call.Return(run)
	return _c
}

// GetYearlySummary provides a mock function with given fields: yearStr
func (_m *MockExpenseService) GetYearlySummary(yearStr string) (*models.YearlySummary, error) {
	ret := _m.Called(yearStr)

	if len(ret) == 0 {
		panic("no return value specified for GetYearlySummary")
	}

	var r0 *models.YearlySummary
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*models.YearlySummary, error)); ok {
		return rf(yearStr)
	}
	if rf, ok := ret.Get(0).(func(string) *models.YearlySummary); ok {
		r0 = rf(yearStr)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.YearlySummary)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(yearStr)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockExpenseService_GetYearlySummary_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetYearlySummary'
type MockExpenseService_GetYearlySummary_Call struct {
	*mock.Call
}

// GetYearlySummary is a helper method to define mock.On call
//   - yearStr string
func (_e *MockExpenseService_Expecter) GetYearlySummary(yearStr interface{}) *MockExpenseService_GetYearlySummary_Call {
	return &MockExpenseService_GetYearlySummary_Call{Call: _e.mock.On("GetYearlySummary", yearStr)}
}

func (_c *MockExpenseService_GetYearlySummary_Call) Run(run func(yearStr string)) *MockExpenseService_GetYearlySummary_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockExpenseService_GetYearlySummary_Call) Return(_a0 *models.YearlySummary, _a1 error) *MockExpenseService_GetYearlySummary_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockExpenseService_GetYearlySummary_Call) RunAndReturn(run func(string) (*models.YearlySummary, error)) *MockExpenseService_GetYearlySummary_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateExpense provides a mock function with given fields: expense
func (_m *MockExpenseService) UpdateExpense(expense *models.Expense) error {
	ret := _m.Called(expense)

	if len(ret) == 0 {
		panic("no return value specified for UpdateExpense")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*models.Expense) error); ok {
		r0 = rf(expense)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockExpenseService_UpdateExpense_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateExpense'
type MockExpenseService_UpdateExpense_Call struct {
	*mock.Call
}

// UpdateExpense is a helper method to define mock.On call
//   - expense *models.Expense
func (_e *MockExpenseService_Expecter) UpdateExpense(expense interface{}) *MockExpenseService_UpdateExpense_Call {
	return &MockExpenseService_UpdateExpense_Call{Call: _e.mock.On("UpdateExpense", expense)}
}

func (_c *MockExpenseService_UpdateExpense_Call) Run(run func(expense *models.Expense)) *MockExpenseService_UpdateExpense_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*models.Expense))
	})
	return _c
}

func (_c *MockExpenseService_UpdateExpense_Call) Return(_a0 error) *MockExpenseService_UpdateExpense_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockExpenseService_UpdateExpense_Call) RunAndReturn(run func(*models.Expense) error) *MockExpenseService_UpdateExpense_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockExpenseService creates a new instance of MockExpenseService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockExpenseService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockExpenseService {
	mock := &MockExpenseService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
