{{define "ai-confirm-content"}}
<div class="card">
    <h1>Confirm AI Parsed Expense</h1>
    <p>Please review and edit the parsed expense data before saving:</p>
    
    {{if .Error}}
    <div class="notification error">
        <strong>Error:</strong> {{.Error}}
    </div>
    {{end}}

    {{if .Success}}
    <div class="notification success">
        <strong>Success!</strong> Expense saved successfully!
    </div>
    {{end}}

    {{if .ParsedExpense}}
    <form action="/ui/ai-confirm" method="POST" class="expense-form">
        <div class="form-row">
            <div class="form-group">
                <label for="amount">Amount *</label>
                <input type="number" name="amount" id="amount" step="0.01" min="0.01" required
                       value="{{.ParsedExpense.Amount}}" onchange="updateTotal()">
                <small class="field-help">Main expense amount in Rupiah</small>
            </div>

            <div class="form-group">
                <label for="additional_amount">Additional Amount <span class="optional">(optional)</span></label>
                <input type="number" name="additional_amount" id="additional_amount" step="0.01" min="0"
                       value="{{.ParsedExpense.AdditionalAmount}}" onchange="updateTotal()">
                <small class="field-help">Tips, tax, or other additional costs</small>
            </div>
        </div>

        <div class="total-display">
            <strong>Total: <span id="total-amount">{{formatCurrency (add .ParsedExpense.Amount .ParsedExpense.AdditionalAmount)}}</span></strong>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label for="category">Category *</label>
                <select name="category" id="category" required>
                    <option value="">Choose a category</option>
                    <option value="Food" {{if eq .ParsedExpense.Category "Food"}}selected{{end}}>Food</option>
                    <option value="Transportation" {{if eq .ParsedExpense.Category "Transportation"}}selected{{end}}>Transportation</option>
                    <option value="Entertainment" {{if eq .ParsedExpense.Category "Entertainment"}}selected{{end}}>Entertainment</option>
                    <option value="Shopping" {{if eq .ParsedExpense.Category "Shopping"}}selected{{end}}>Shopping</option>
                    <option value="Bills" {{if eq .ParsedExpense.Category "Bills"}}selected{{end}}>Bills</option>
                    <option value="Healthcare" {{if eq .ParsedExpense.Category "Healthcare"}}selected{{end}}>Healthcare</option>
                    <option value="Education" {{if eq .ParsedExpense.Category "Education"}}selected{{end}}>Education</option>
                    <option value="Travel" {{if eq .ParsedExpense.Category "Travel"}}selected{{end}}>Travel</option>
                    <option value="Other" {{if eq .ParsedExpense.Category "Other"}}selected{{end}}>Other</option>
                </select>
                <small class="field-help">What type of expense is this?</small>
            </div>

            <div class="form-group">
                <label for="subcategory">Subcategory <span class="optional">(optional)</span></label>
                <input type="text" name="subcategory" id="subcategory"
                       value="{{.ParsedExpense.Subcategory}}" placeholder="e.g., Lunch, Gas, Movies">
                <small class="field-help">More specific category for better tracking</small>
            </div>
        </div>

        <div class="form-group">
            <label for="description">Description <span class="optional">(optional)</span></label>
            <input type="text" name="description" id="description"
                   value="{{.ParsedExpense.Description}}" placeholder="Additional details about this expense">
            <small class="field-help">Any additional notes or details</small>
        </div>

        <div class="form-group">
            <label for="date">Date *</label>
            <input type="date" name="date" id="date" required value="{{.ParsedExpense.Date}}">
            <small class="field-help">When did this expense occur?</small>
        </div>

        <div class="ai-confidence">
            <small>AI Confidence: {{printf "%.0f" (mul .ParsedExpense.Confidence 100)}}%</small>
        </div>

        <div class="form-actions">
            <button type="submit" class="btn btn-primary">Save Expense</button>
            <a href="/ui/ai-input" class="btn btn-secondary">Back to Input</a>
            <a href="/ui" class="btn btn-secondary">Cancel</a>
        </div>
    </form>
    {{end}}
</div>

<script>
function updateTotal() {
    const amount = parseFloat(document.getElementById('amount').value) || 0;
    const additionalAmount = parseFloat(document.getElementById('additional_amount').value) || 0;
    const total = amount + additionalAmount;
    
    // Format as Indonesian Rupiah
    const formatted = new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(total).replace('IDR', 'Rp');
    
    document.getElementById('total-amount').textContent = formatted;
}

// Initialize total on page load
document.addEventListener('DOMContentLoaded', function() {
    updateTotal();
});
</script>

<style>
.ai-confidence {
    margin: 1rem 0;
    padding: 0.5rem;
    background: #e7f3ff;
    border-radius: 4px;
    text-align: center;
}

.ai-confidence small {
    color: #007acc;
    font-weight: 500;
}

.total-display {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    text-align: center;
    margin: 1rem 0;
    border: 2px solid #007acc;
}

.total-display strong {
    font-size: 1.2rem;
    color: #007acc;
}
</style>
{{end}}
