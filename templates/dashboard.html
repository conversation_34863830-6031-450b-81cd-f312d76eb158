{{define "dashboard-content"}}
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-value">{{formatCurrency .TotalExpenses}}</div>
        <div class="stat-label">Total This Month</div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{{.ExpenseCount}}</div>
        <div class="stat-label">Expenses This Month</div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{{formatCurrency .AverageExpense}}</div>
        <div class="stat-label">Average Expense</div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{{.TopCategory}}</div>
        <div class="stat-label">Top Category</div>
    </div>
</div>

<div class="card">
    <h2>Quick Actions</h2>
    <div style="display: flex; gap: 1rem; margin-top: 1rem;">
        <a href="/ui/add" class="btn">Add New Expense</a>
        <a href="/ui/expenses" class="btn btn-secondary">View All Expenses</a>
        <a href="/ui/summary" class="btn btn-secondary">View Summary</a>
    </div>
</div>

<div class="card">
    <h2>Recent Expenses</h2>
    {{if .RecentExpenses}}
    <table class="table">
        <thead>
            <tr>
                <th>Date</th>
                <th>Description</th>
                <th>Category</th>
                <th>Amount</th>
                <th>Additional</th>
            </tr>
        </thead>
        <tbody>
            {{range .RecentExpenses}}
            <tr>
                <td>{{.Date}}</td>
                <td>{{.Description}}</td>
                <td>{{.Category}}{{if .Subcategory}} - {{.Subcategory}}{{end}}</td>
                <td>{{formatCurrency .Amount}}</td>
                <td>{{formatCurrency .AdditionalAmount}}</td>
            </tr>
            {{end}}
        </tbody>
    </table>
    <div style="margin-top: 1rem;">
        <a href="/ui/expenses" class="btn btn-secondary">View All Expenses</a>
    </div>
    {{else}}
    <p>No expenses found. <a href="/ui/add">Add your first expense</a>!</p>
    {{end}}
</div>

<div class="card">
    <h2>Category Breakdown (This Month)</h2>
    {{if .CategoryBreakdown}}
    <table class="table">
        <thead>
            <tr>
                <th>Category</th>
                <th>Amount</th>
                <th>Percentage</th>
                <th>Top Subcategory</th>
            </tr>
        </thead>
        <tbody>
            {{range .CategoryBreakdown}}
            <tr>
                <td><strong>{{.Category}}</strong></td>
                <td>{{formatCurrency .Amount}}</td>
                <td>{{printf "%.1f" .Percentage}}%</td>
                <td>
                    {{if .TopSubcategory}}
                        {{.TopSubcategory}} ({{formatCurrency .TopSubAmount}})
                    {{else}}
                        -
                    {{end}}
                </td>
            </tr>
            {{end}}
        </tbody>
    </table>
    {{else}}
    <p>No category data available.</p>
    {{end}}
</div>
{{end}}
